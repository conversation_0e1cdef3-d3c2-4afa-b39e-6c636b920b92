# Eclipse AI Agent Plugin - Installation Guide

This guide provides detailed instructions for installing and configuring the Eclipse AI Agent Plugin.

## Prerequisites

### System Requirements
- **Eclipse IDE**: 2019-12 (4.14) or compatible version
- **Java**: Java 8 (JDK 1.8) or higher
- **Operating System**: Windows, Linux, or macOS
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Network**: Internet connection for AI provider access

### Development Requirements (if building from source)
- **Maven**: 3.6.0 or higher
- **Git**: For cloning the repository

## Installation Methods

### Method 1: Pre-built Plugin (Recommended)

1. **Download the Plugin**
   - Download the latest release from the GitHub releases page
   - Extract the ZIP file to a temporary location

2. **Install Dependencies**
   - Copy all JAR files from the `lib/` folder to your Eclipse `dropins/` directory
   - The required dependencies are:
     - `httpclient-4.5.14.jar`
     - `httpcore-4.4.16.jar`
     - `gson-2.10.1.jar`
     - `commons-logging-1.2.jar`

3. **Install the Plugin**
   - Copy `aiagent-1.0.0-SNAPSHOT.jar` to your Eclipse `dropins/` directory
   - The `dropins/` directory is typically located at:
     - Windows: `C:\eclipse\dropins\`
     - Linux: `/opt/eclipse/dropins/` or `~/eclipse/dropins/`
     - macOS: `/Applications/Eclipse.app/Contents/Eclipse/dropins/`

4. **Restart Eclipse**
   - Close Eclipse completely
   - Start Eclipse again
   - The plugin should be automatically loaded

### Method 2: Eclipse Marketplace (Future)

*Note: This method will be available once the plugin is published to the Eclipse Marketplace.*

1. Open Eclipse
2. Go to **Help > Eclipse Marketplace**
3. Search for "AI Agent"
4. Click **Install** on the Eclipse AI Agent Plugin
5. Follow the installation wizard
6. Restart Eclipse when prompted

### Method 3: Install New Software

1. **Prepare Update Site**
   - Extract the plugin files to a local directory
   - Or use a hosted update site URL (when available)

2. **Install via Eclipse**
   - Open Eclipse
   - Go to **Help > Install New Software**
   - Click **Add** to add a new repository
   - Choose **Local** and browse to the plugin directory
   - Or enter the update site URL
   - Select the AI Agent Plugin from the list
   - Follow the installation wizard
   - Restart Eclipse when prompted

### Method 4: Build from Source

1. **Clone the Repository**
   ```bash
   git clone https://github.com/fartech/eclipse-ai-agent.git
   cd eclipse-ai-agent
   ```

2. **Build the Plugin**
   
   **Windows:**
   ```cmd
   build.bat
   ```
   
   **Linux/macOS:**
   ```bash
   chmod +x build.sh
   ./build.sh
   ```
   
   **Manual Maven Build:**
   ```bash
   mvn clean dependency:copy-dependencies compile package
   ```

3. **Install Built Plugin**
   - Follow Method 1 using the generated JAR file from `target/`

## Configuration

### Initial Setup

1. **Open Preferences**
   - Go to **Window > Preferences** (Windows/Linux)
   - Or **Eclipse > Preferences** (macOS)

2. **Navigate to AI Agent Settings**
   - Expand the tree and find **AI Agent**
   - Click on it to open the configuration page

3. **Configure AI Provider**
   - Select your preferred AI provider (OpenAI, Anthropic, etc.)
   - Enter the API endpoint URL
   - Enter your API key (stored securely)
   - Choose the model to use
   - Adjust timeout and token limits as needed

### AI Provider Setup

#### OpenAI Configuration
- **Provider Type**: OpenAI
- **Endpoint**: `https://api.openai.com/v1/chat/completions`
- **Model**: `gpt-4` or `gpt-3.5-turbo`
- **API Key**: Your OpenAI API key (starts with `sk-`)

#### Anthropic Configuration
- **Provider Type**: Anthropic
- **Endpoint**: `https://api.anthropic.com/v1/messages`
- **Model**: `claude-3-sonnet-20240229`
- **API Key**: Your Anthropic API key (starts with `sk-ant-`)

#### Azure OpenAI Configuration
- **Provider Type**: Azure
- **Endpoint**: Your Azure OpenAI endpoint
- **Model**: Your deployed model name
- **API Key**: Your Azure API key

#### Local LLM Configuration
- **Provider Type**: Local
- **Endpoint**: Your local LLM endpoint (e.g., `http://localhost:11434/api/generate`)
- **Model**: Your local model name
- **API Key**: Leave empty or as required by your local setup

### Security Settings

1. **Approval Requirements**
   - Enable "Require approval for all changes" for maximum safety
   - Configure sensitive data patterns to scrub

2. **Data Privacy**
   - Enable data scrubbing if working with sensitive code
   - Configure patterns for sensitive information (passwords, keys, etc.)

3. **Logging**
   - Set appropriate log level (INFO recommended for production)
   - Enable console logging for debugging
   - Disable AI interaction logging for privacy

### Agent Behavior

1. **Autonomous Mode**
   - Enable for full autonomous operation
   - Set maximum execution steps (10 recommended)
   - Enable self-correction for error recovery
   - Set retry attempts (3 recommended)

2. **UI Preferences**
   - Enable detailed progress information
   - Auto-open diff view for changes
   - Enable syntax highlighting in chat

## Verification

### Test Installation

1. **Open AI Agent Views**
   - Go to **Window > Show View > Other**
   - Expand **AI Agent** category
   - Open **AI Agent Chat**
   - Open **AI Agent Progress**
   - Open **AI Agent Diff Approval**

2. **Test Basic Functionality**
   - Type a simple message in the chat view: "Hello, can you help me?"
   - Verify that the AI responds (requires valid API key)
   - Check that progress is shown in the progress view

3. **Test Context Menu**
   - Right-click on a Java file in Project Explorer
   - Verify **AI Agent** submenu appears
   - Try "Send to AI Agent" action

### Keyboard Shortcuts

- **Ctrl+Alt+A**: Open AI Agent Chat
- **Ctrl+Alt+C**: Cancel current task

## Troubleshooting

### Common Issues

#### Plugin Not Loading
- Check Eclipse error log: **Window > Show View > Error Log**
- Verify all dependencies are in the `dropins/` folder
- Ensure Eclipse version compatibility (2019-12 or later)
- Check Java version (Java 8 or higher required)

#### API Connection Issues
- Verify API key is correct and has sufficient credits
- Check network connectivity
- Verify endpoint URL is correct
- Check firewall/proxy settings

#### Performance Issues
- Reduce max tokens in preferences
- Disable detailed logging
- Increase Eclipse memory allocation (`-Xmx` parameter)

#### UI Issues
- Reset perspective: **Window > Perspective > Reset Perspective**
- Clear workspace metadata (backup first): Delete `.metadata` folder
- Check SWT/UI thread errors in error log

### Getting Help

1. **Console Output**
   - Check **AI Agent** console for detailed logs
   - Enable debug logging in preferences

2. **Error Logs**
   - Check Eclipse error log for exceptions
   - Look for AI Agent related entries

3. **Support Channels**
   - GitHub Issues: Report bugs and feature requests
   - Documentation: Check the README and wiki
   - Community: Join discussions in GitHub Discussions

## Uninstallation

### Remove Plugin

1. **Manual Removal**
   - Delete the plugin JAR from `dropins/` folder
   - Remove dependency JARs if not used by other plugins
   - Restart Eclipse

2. **Via Eclipse**
   - Go to **Help > About Eclipse IDE**
   - Click **Installation Details**
   - Select the AI Agent Plugin
   - Click **Uninstall**
   - Follow the wizard and restart Eclipse

### Clean Configuration

1. **Remove Preferences**
   - The preferences will be automatically removed
   - Or manually delete from workspace `.metadata/.plugins/org.eclipse.core.runtime/.settings/`

2. **Remove Secure Storage**
   - API keys are stored in Eclipse's secure storage
   - They will be automatically cleaned up

## Next Steps

After successful installation:

1. **Read the User Guide**: Check `README.md` for usage instructions
2. **Try Examples**: Start with simple requests to familiarize yourself
3. **Configure for Your Workflow**: Adjust settings based on your development needs
4. **Explore Features**: Try different AI providers and autonomous modes
5. **Provide Feedback**: Report issues and suggest improvements

## Support

For additional help:
- **Documentation**: See README.md and GitHub wiki
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join community discussions
- **Email**: Contact the development team (if applicable)
