# Eclipse Plugin Issues - Troubleshooting Guide

This guide addresses common Eclipse plugin development issues and their solutions.

## 🔧 Issues Fixed

✅ **Java 8 Compatibility**
- Fixed `var` keyword usage (not available in Java 8)
- Fixed deprecated `JsonParser()` constructor
- Updated to use `JsonParser.parseString()` static method

✅ **MessageBox API**
- Fixed `setTitle()` method (should be `setText()`)

✅ **Stack Trace Printing**
- Fixed `printStackTrace(MessageConsoleStream)` incompatibility
- Now converts stack trace to string before printing

✅ **Optional Dependencies**
- Made optional dependencies in MANIFEST.MF to avoid missing bundle errors

## 🚨 Remaining Eclipse Platform Issues

The following issues indicate that Eclipse is not recognizing this as a proper plugin project:

### Issue: `IFileEditorInput cannot be resolved to a type`
### Issue: `org.eclipse.ui.ide cannot be resolved`

## 🛠️ Solutions

### Solution 1: Set Up as Eclipse Plugin Project

1. **Import as Plugin Project:**
   - File → Import → Plug-in Development → Projects from File System
   - Select your project directory
   - Make sure "Search for nested projects" is checked

2. **Set Target Platform:**
   - Window → Preferences → Plug-in Development → Target Platform
   - Select "Running Platform" or create a new target platform
   - Make sure Eclipse 2019-12 (4.14) is selected

3. **Verify Project Nature:**
   - Right-click project → Properties → Project Natures
   - Should include:
     - `org.eclipse.pde.PluginNature`
     - `org.eclipse.jdt.core.javanature`

### Solution 2: Manual Project Setup

If the above doesn't work, manually configure the project:

1. **Add Plugin Nature:**
   - Right-click project → Configure → Convert to Plug-in Projects

2. **Check Build Path:**
   - Right-click project → Properties → Java Build Path
   - Libraries tab should show:
     - Plug-in Dependencies
     - JRE System Library [JavaSE-1.8]

3. **Verify MANIFEST.MF:**
   - Open META-INF/MANIFEST.MF
   - Should open in the Plug-in Manifest Editor
   - If it opens as text, the project is not recognized as a plugin

### Solution 3: Create New Plugin Project

If issues persist, create a fresh plugin project:

1. **Create New Plugin Project:**
   - File → New → Other → Plug-in Development → Plug-in Project
   - Use existing source: Browse to your src folder
   - Copy MANIFEST.MF and plugin.xml

2. **Copy Source Files:**
   - Copy all files from src/ to the new project
   - Copy META-INF/MANIFEST.MF
   - Copy plugin.xml
   - Copy build.properties

### Solution 4: Workspace Issues

Sometimes workspace corruption causes these issues:

1. **Clean Workspace:**
   - Project → Clean → Clean all projects
   - Restart Eclipse

2. **Refresh Project:**
   - Right-click project → Refresh (F5)

3. **Reset Workspace:**
   - Close Eclipse
   - Delete `.metadata/.plugins/org.eclipse.core.resources/.projects/[project-name]`
   - Restart Eclipse and re-import project

## 🔍 Verification Steps

After applying solutions, verify:

1. **Project Explorer:**
   - Project should have plugin icon (not folder icon)
   - Should show "Plug-in Dependencies" in build path

2. **MANIFEST.MF:**
   - Should open in Plug-in Manifest Editor (tabbed interface)
   - Dependencies tab should show all required bundles

3. **Build Path:**
   - Properties → Java Build Path → Libraries
   - Should show "Plug-in Dependencies" entry
   - Should NOT show individual Eclipse JARs

4. **Error Log:**
   - Window → Show View → Error Log
   - Should not show bundle resolution errors

## 🎯 Quick Fix Commands

Run these in Eclipse if the GUI methods don't work:

1. **Convert to Plugin Project:**
   ```
   Right-click project → Configure → Convert to Plug-in Projects
   ```

2. **Add Plugin Nature (if missing):**
   Edit `.project` file and add:
   ```xml
   <nature>org.eclipse.pde.PluginNature</nature>
   <nature>org.eclipse.jdt.core.javanature</nature>
   ```

3. **Force Refresh:**
   ```
   Project → Clean → Select project → Clean
   ```

## 📋 Expected Project Structure

Your project should look like this in Eclipse:

```
com.fartech.eclipse.aiagent [Plugin Project Icon]
├── 📁 src
│   └── 📁 com.fartech.eclipse.aiagent
├── 📁 Plug-in Dependencies [Auto-generated]
├── 📁 JRE System Library [JavaSE-1.8]
├── 📄 META-INF/MANIFEST.MF
├── 📄 plugin.xml
├── 📄 build.properties
└── 📁 lib
    ├── httpclient-4.5.14.jar
    ├── httpcore-4.4.16.jar
    ├── gson-2.10.1.jar
    └── commons-logging-1.2.jar
```

## 🚀 Testing the Fix

1. **Open MANIFEST.MF:**
   - Should open in Plug-in Manifest Editor (not text editor)
   - Dependencies tab should show all bundles as resolved

2. **Check Build Path:**
   - No red X marks on project
   - No "missing required library" errors

3. **Run Plugin:**
   - Right-click project → Run As → Eclipse Application
   - New Eclipse instance should start with your plugin

## 📞 Still Having Issues?

If problems persist:

1. **Check Eclipse Version:**
   - Help → About Eclipse IDE
   - Should be 2019-12 (4.14) or compatible

2. **Check Java Version:**
   - Window → Preferences → Java → Installed JREs
   - Should show Java 8 (1.8)

3. **Check PDE Installation:**
   - Help → About Eclipse IDE → Installation Details
   - Should include "Eclipse PDE" features

4. **Create Minimal Test:**
   - Create new simple plugin project
   - If that works, compare configurations

## 🔄 Alternative: Use Eclipse Plugin Development Environment

If you're not using Eclipse PDE:

1. **Download Eclipse for RCP and RAP Developers:**
   - Includes all plugin development tools
   - Pre-configured for plugin development

2. **Install PDE in existing Eclipse:**
   - Help → Install New Software
   - Add Eclipse release repository
   - Install "General Purpose Tools" → "Eclipse Plug-in Development Environment"

This should resolve all Eclipse platform-related compilation issues.
