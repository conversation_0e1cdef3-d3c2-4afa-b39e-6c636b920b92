<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension
         point="org.eclipse.ui.views">
      <category
            name="AI Agent"
            id="com.fartech.eclipse.aiagent.category">
      </category>
      <view
            name="AI Agent Chat"
            icon="icons/ai_agent.png"
            category="com.fartech.eclipse.aiagent.category"
            class="com.fartech.eclipse.aiagent.ui.views.ChatView"
            id="com.fartech.eclipse.aiagent.ui.views.ChatView">
      </view>
      <view
            name="AI Agent Progress"
            icon="icons/progress.png"
            category="com.fartech.eclipse.aiagent.category"
            class="com.fartech.eclipse.aiagent.ui.views.ProgressView"
            id="com.fartech.eclipse.aiagent.ui.views.ProgressView">
      </view>
      <view
            name="AI Agent Diff Approval"
            icon="icons/diff.png"
            category="com.fartech.eclipse.aiagent.category"
            class="com.fartech.eclipse.aiagent.ui.views.DiffApprovalView"
            id="com.fartech.eclipse.aiagent.ui.views.DiffApprovalView">
      </view>
   </extension>
   
   <extension
         point="org.eclipse.ui.preferencePages">
      <page
            name="AI Agent"
            class="com.fartech.eclipse.aiagent.ui.preferences.AIAgentPreferencePage"
            id="com.fartech.eclipse.aiagent.preferences">
      </page>
   </extension>
   
   <extension
         point="org.eclipse.ui.popupMenus">
      <objectContribution
            objectClass="org.eclipse.core.resources.IResource"
            id="com.fartech.eclipse.aiagent.popup">
         <menu
               label="AI Agent"
               path="additions"
               id="com.fartech.eclipse.aiagent.menu">
            <separator
                  name="group1">
            </separator>
         </menu>
         <action
               label="Send to AI Agent"
               class="com.fartech.eclipse.aiagent.ui.actions.SendToAIAction"
               menubarPath="com.fartech.eclipse.aiagent.menu/group1"
               id="com.fartech.eclipse.aiagent.sendToAI">
         </action>
         <action
               label="Analyze with AI"
               class="com.fartech.eclipse.aiagent.ui.actions.AnalyzeWithAIAction"
               menubarPath="com.fartech.eclipse.aiagent.menu/group1"
               id="com.fartech.eclipse.aiagent.analyzeWithAI">
         </action>
      </objectContribution>
   </extension>
   
   <extension
         point="org.eclipse.ui.editorActions">
      <editorContribution
            targetID="org.eclipse.jdt.ui.CompilationUnitEditor"
            id="com.fartech.eclipse.aiagent.editorActions">
         <menu
               label="AI Agent"
               path="additions"
               id="com.fartech.eclipse.aiagent.editorMenu">
            <separator
                  name="group1">
            </separator>
         </menu>
         <action
               label="Explain Code"
               class="com.fartech.eclipse.aiagent.ui.actions.ExplainCodeAction"
               menubarPath="com.fartech.eclipse.aiagent.editorMenu/group1"
               id="com.fartech.eclipse.aiagent.explainCode">
         </action>
         <action
               label="Refactor with AI"
               class="com.fartech.eclipse.aiagent.ui.actions.RefactorWithAIAction"
               menubarPath="com.fartech.eclipse.aiagent.editorMenu/group1"
               id="com.fartech.eclipse.aiagent.refactorWithAI">
         </action>
      </editorContribution>
   </extension>
   
   <extension
         point="org.eclipse.ui.commands">
      <category
            name="AI Agent Commands"
            id="com.fartech.eclipse.aiagent.commands.category">
      </category>
      <command
            name="Open AI Agent Chat"
            categoryId="com.fartech.eclipse.aiagent.commands.category"
            id="com.fartech.eclipse.aiagent.commands.openChat">
      </command>
      <command
            name="Cancel AI Task"
            categoryId="com.fartech.eclipse.aiagent.commands.category"
            id="com.fartech.eclipse.aiagent.commands.cancelTask">
      </command>
   </extension>
   
   <extension
         point="org.eclipse.ui.handlers">
      <handler
            commandId="com.fartech.eclipse.aiagent.commands.openChat"
            class="com.fartech.eclipse.aiagent.ui.handlers.OpenChatHandler">
      </handler>
      <handler
            commandId="com.fartech.eclipse.aiagent.commands.cancelTask"
            class="com.fartech.eclipse.aiagent.ui.handlers.CancelTaskHandler">
      </handler>
   </extension>
   
   <extension
         point="org.eclipse.ui.bindings">
      <key
            commandId="com.fartech.eclipse.aiagent.commands.openChat"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration"
            sequence="Ctrl+Alt+A">
      </key>
      <key
            commandId="com.fartech.eclipse.aiagent.commands.cancelTask"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration"
            sequence="Ctrl+Alt+C">
      </key>
   </extension>
</plugin>
