package com.fartech.eclipse.aiagent.ui.views;

import java.util.concurrent.Future;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.action.Separator;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.part.ViewPart;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.AIAgentCore;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Main chat view for interacting with the AI Agent.
 */
public class ChatView extends ViewPart {
    
    public static final String ID = "com.fartech.eclipse.aiagent.ui.views.ChatView";
    
    private AIAgentLogger logger;
    private AIAgentCore aiAgentCore;
    
    private StyledText chatHistory;
    private Text inputText;
    private Button sendButton;
    private Button clearButton;
    private Label statusLabel;
    
    private Future<?> currentTask;
    
    @Override
    public void createPartControl(Composite parent) {
        logger = Activator.getDefault().getLogger();
        aiAgentCore = Activator.getDefault().getAIAgentCore();
        
        // Create main container
        Composite container = new Composite(parent, SWT.NONE);
        container.setLayout(new GridLayout(1, false));
        
        // Create sash form for resizable areas
        SashForm sashForm = new SashForm(container, SWT.VERTICAL);
        sashForm.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        // Create chat history area
        createChatHistoryArea(sashForm);
        
        // Create input area
        createInputArea(sashForm);
        
        // Set sash weights (70% chat history, 30% input)
        sashForm.setWeights(new int[] { 70, 30 });
        
        // Create status area
        createStatusArea(container);
        
        // Create actions
        createActions();
        
        // Initialize with welcome message
        appendToChatHistory("AI Agent", "Welcome! I'm your AI development assistant. How can I help you today?", false);
        
        logger.info("ChatView created successfully");
    }
    
    private void createChatHistoryArea(Composite parent) {
        Composite historyComposite = new Composite(parent, SWT.NONE);
        historyComposite.setLayout(new GridLayout(1, false));
        
        Label historyLabel = new Label(historyComposite, SWT.NONE);
        historyLabel.setText("Conversation History:");
        historyLabel.setLayoutData(new GridData(SWT.LEFT, SWT.TOP, false, false));
        
        chatHistory = new StyledText(historyComposite, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL | SWT.READ_ONLY | SWT.WRAP);
        chatHistory.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        chatHistory.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
    }
    
    private void createInputArea(Composite parent) {
        Composite inputComposite = new Composite(parent, SWT.NONE);
        inputComposite.setLayout(new GridLayout(1, false));
        
        Label inputLabel = new Label(inputComposite, SWT.NONE);
        inputLabel.setText("Your Message:");
        inputLabel.setLayoutData(new GridData(SWT.LEFT, SWT.TOP, false, false));
        
        // Create input text area
        inputText = new Text(inputComposite, SWT.BORDER | SWT.MULTI | SWT.V_SCROLL | SWT.WRAP);
        GridData inputData = new GridData(SWT.FILL, SWT.FILL, true, true);
        inputData.heightHint = 80;
        inputText.setLayoutData(inputData);
        
        // Add key listener for Ctrl+Enter to send
        inputText.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.keyCode == SWT.CR && (e.stateMask & SWT.CTRL) != 0) {
                    sendMessage();
                }
            }
        });
        
        // Create button area
        Composite buttonComposite = new Composite(inputComposite, SWT.NONE);
        buttonComposite.setLayout(new GridLayout(2, false));
        buttonComposite.setLayoutData(new GridData(SWT.RIGHT, SWT.TOP, false, false));
        
        sendButton = new Button(buttonComposite, SWT.PUSH);
        sendButton.setText("Send (Ctrl+Enter)");
        sendButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                sendMessage();
            }
        });
        
        clearButton = new Button(buttonComposite, SWT.PUSH);
        clearButton.setText("Clear");
        clearButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                clearChat();
            }
        });
    }
    
    private void createStatusArea(Composite parent) {
        statusLabel = new Label(parent, SWT.NONE);
        statusLabel.setText("Ready");
        statusLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
    }
    
    private void createActions() {
        IMenuManager menuManager = getViewSite().getActionBars().getMenuManager();
        IToolBarManager toolBarManager = getViewSite().getActionBars().getToolBarManager();
        
        // Clear action
        Action clearAction = new Action("Clear Chat") {
            @Override
            public void run() {
                clearChat();
            }
        };
        clearAction.setToolTipText("Clear the chat history");
        
        // Cancel action
        Action cancelAction = new Action("Cancel Task") {
            @Override
            public void run() {
                cancelCurrentTask();
            }
        };
        cancelAction.setToolTipText("Cancel the current AI task");
        
        // Add to menu and toolbar
        menuManager.add(clearAction);
        menuManager.add(new Separator());
        menuManager.add(cancelAction);
        
        toolBarManager.add(clearAction);
        toolBarManager.add(cancelAction);
    }
    
    private void sendMessage() {
        String message = inputText.getText().trim();
        if (message.isEmpty()) {
            return;
        }
        
        // Check if AI Agent Core is available
        if (aiAgentCore == null) {
            appendToChatHistory("System", "AI Agent Core is not available. Please check the plugin configuration.", true);
            return;
        }
        
        // Check if a task is already running
        if (currentTask != null && !currentTask.isDone()) {
            appendToChatHistory("System", "A task is already running. Please wait or cancel it first.", true);
            return;
        }
        
        // Add user message to chat
        appendToChatHistory("You", message, false);
        
        // Clear input
        inputText.setText("");
        
        // Update UI state
        updateUIState(true);
        
        try {
            // Process the message asynchronously
            currentTask = aiAgentCore.processUserRequest(message);
            
            // Monitor the task completion
            Display.getCurrent().asyncExec(new Runnable() {
                @Override
                public void run() {
                    monitorTaskCompletion();
                }
            });
            
        } catch (Exception e) {
            logger.error("Error processing user request", e);
            appendToChatHistory("System", "Error processing request: " + e.getMessage(), true);
            updateUIState(false);
        }
    }
    
    private void monitorTaskCompletion() {
        if (currentTask == null || currentTask.isDone()) {
            // Task completed
            updateUIState(false);
            
            if (currentTask != null) {
                try {
                    currentTask.get(); // Check for exceptions
                    appendToChatHistory("AI Agent", "Task completed successfully.", false);
                } catch (Exception e) {
                    logger.error("Task completed with error", e);
                    appendToChatHistory("AI Agent", "Task completed with error: " + e.getMessage(), true);
                }
            }
            
            return;
        }
        
        // Update status
        if (aiAgentCore != null) {
            String status = aiAgentCore.getCurrentStatus();
            updateStatus(status);
        }
        
        // Schedule next check
        Display.getCurrent().timerExec(1000, new Runnable() {
            @Override
            public void run() {
                monitorTaskCompletion();
            }
        });
    }
    
    private void cancelCurrentTask() {
        if (currentTask != null && !currentTask.isDone()) {
            currentTask.cancel(true);
            
            if (aiAgentCore != null) {
                aiAgentCore.cancelCurrentTask();
            }
            
            appendToChatHistory("System", "Task cancelled by user.", false);
            updateUIState(false);
        }
    }
    
    private void clearChat() {
        chatHistory.setText("");
        appendToChatHistory("AI Agent", "Chat cleared. How can I help you?", false);
    }
    
    private void appendToChatHistory(String sender, String message, boolean isError) {
        if (chatHistory.isDisposed()) {
            return;
        }
        
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!chatHistory.isDisposed()) {
                    String timestamp = java.time.LocalTime.now().toString().substring(0, 8);
                    String formattedMessage = String.format("[%s] %s: %s\n\n", timestamp, sender, message);
                    
                    chatHistory.append(formattedMessage);
                    
                    // Auto-scroll to bottom
                    chatHistory.setTopIndex(chatHistory.getLineCount() - 1);
                }
            }
        });
    }
    
    private void updateUIState(boolean taskRunning) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!sendButton.isDisposed()) {
                    sendButton.setEnabled(!taskRunning);
                }
                if (!inputText.isDisposed()) {
                    inputText.setEnabled(!taskRunning);
                }
                
                if (taskRunning) {
                    updateStatus("Processing request...");
                } else {
                    updateStatus("Ready");
                }
            }
        });
    }
    
    private void updateStatus(String status) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!statusLabel.isDisposed()) {
                    statusLabel.setText(status);
                }
            }
        });
    }
    
    @Override
    public void setFocus() {
        if (inputText != null && !inputText.isDisposed()) {
            inputText.setFocus();
        }
    }
    
    @Override
    public void dispose() {
        if (currentTask != null && !currentTask.isDone()) {
            currentTask.cancel(true);
        }
        super.dispose();
    }
}
