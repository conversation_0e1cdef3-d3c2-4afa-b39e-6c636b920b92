package com.fartech.eclipse.aiagent.ui.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.AIAgentCore;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * <PERSON><PERSON> for cancelling the current AI Agent task.
 */
public class CancelTaskHandler extends AbstractHandler {
    
    private AIAgentLogger logger;
    
    public CancelTaskHandler() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public Object execute(ExecutionEvent event) throws ExecutionException {
        try {
            AIAgentCore aiAgentCore = Activator.getDefault().getAIAgentCore();
            
            if (aiAgentCore != null) {
                if (aiAgentCore.isTaskRunning()) {
                    aiAgentCore.cancelCurrentTask();
                    logger.info("Cancelled current AI Agent task");
                } else {
                    logger.info("No AI Agent task is currently running");
                }
            } else {
                logger.warn("AI Agent Core is not available");
            }
            
        } catch (Exception e) {
            logger.error("Failed to cancel AI Agent task", e);
            throw new ExecutionException("Failed to cancel AI Agent task", e);
        }
        
        return null;
    }
}
