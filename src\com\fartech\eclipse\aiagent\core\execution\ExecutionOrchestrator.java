package com.fartech.eclipse.aiagent.core.execution;

import java.util.concurrent.atomic.AtomicBoolean;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.adapters.EditorAdapter;
import com.fartech.eclipse.aiagent.adapters.FileSystemAdapter;
import com.fartech.eclipse.aiagent.adapters.TerminalAdapter;
import com.fartech.eclipse.aiagent.core.context.ContextManager;
import com.fartech.eclipse.aiagent.core.feedback.FeedbackLoop;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.planning.ExecutionPlan;
import com.fartech.eclipse.aiagent.core.planning.ExecutionStep;
import com.fartech.eclipse.aiagent.core.planning.ExecutionStep.ExecutionStatus;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;

/**
 * Orchestrates the execution of planned steps.
 * Manages the sequential execution of steps and handles errors.
 */
public class ExecutionOrchestrator {
    
    private final AIAgentLogger logger;
    private final ContextManager contextManager;
    private final FileSystemAdapter fileSystemAdapter;
    private final EditorAdapter editorAdapter;
    private final TerminalAdapter terminalAdapter;
    
    private FeedbackLoop feedbackLoop;
    private ExecutionPlan currentPlan;
    private final AtomicBoolean executing = new AtomicBoolean(false);
    private final AtomicBoolean cancelled = new AtomicBoolean(false);
    private boolean initialized = false;
    
    public ExecutionOrchestrator(ContextManager contextManager) {
        this.logger = Activator.getDefault().getLogger();
        this.contextManager = contextManager;
        this.fileSystemAdapter = contextManager.getFileSystemAdapter();
        this.editorAdapter = contextManager.getEditorAdapter();
        this.terminalAdapter = contextManager.getTerminalAdapter();
    }
    
    /**
     * Initialize the execution orchestrator
     */
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("ExecutionOrchestrator already initialized");
            return;
        }
        
        try {
            logger.info("Initializing ExecutionOrchestrator...");
            
            // Verify dependencies
            if (contextManager == null) {
                throw new Exception("ContextManager is required");
            }
            
            initialized = true;
            logger.info("ExecutionOrchestrator initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize ExecutionOrchestrator", e);
            throw e;
        }
    }
    
    /**
     * Set the feedback loop (called by AIAgentCore)
     */
    public void setFeedbackLoop(FeedbackLoop feedbackLoop) {
        this.feedbackLoop = feedbackLoop;
    }
    
    /**
     * Execute the given plan
     * @param plan the execution plan to execute
     * @throws Exception if execution fails
     */
    public void executePlan(ExecutionPlan plan) throws Exception {
        if (!initialized) {
            throw new IllegalStateException("ExecutionOrchestrator not initialized");
        }
        
        if (plan == null) {
            throw new IllegalArgumentException("Execution plan cannot be null");
        }
        
        if (executing.getAndSet(true)) {
            throw new IllegalStateException("Execution already in progress");
        }
        
        try {
            logger.info("Starting execution of plan: " + plan.getOriginalGoal());
            currentPlan = plan;
            cancelled.set(false);
            
            // Execute steps sequentially
            while (plan.hasMoreSteps() && !cancelled.get()) {
                ExecutionStep step = plan.getCurrentStep();
                
                if (step == null) {
                    break;
                }
                
                try {
                    executeStep(step);
                    
                    // Move to next step if current step completed successfully
                    if (step.getStatus() == ExecutionStatus.COMPLETED) {
                        plan.moveToNextStep();
                    } else if (step.getStatus() == ExecutionStatus.FAILED) {
                        // Handle step failure
                        handleStepFailure(step, plan);
                        break;
                    } else if (step.getStatus() == ExecutionStatus.SKIPPED) {
                        // Move to next step if skipped
                        plan.moveToNextStep();
                    }
                    
                } catch (InterruptedException e) {
                    logger.info("Execution interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("Error executing step " + step.getStepNumber(), e);
                    step.markFailed("Execution error: " + e.getMessage());
                    handleStepFailure(step, plan);
                    break;
                }
            }
            
            if (cancelled.get()) {
                logger.info("Execution was cancelled");
            } else if (plan.isComplete()) {
                logger.info("Plan execution completed successfully");
            }
            
        } finally {
            executing.set(false);
            currentPlan = null;
        }
    }
    
    private void executeStep(ExecutionStep step) throws Exception {
        logger.info("Executing step " + step.getStepNumber() + ": " + step.getOperation());
        
        step.markStarted();
        
        try {
            // Check if step requires user approval
            if (step.requiresApproval() && shouldRequireApproval()) {
                boolean approved = requestUserApproval(step);
                if (!approved) {
                    step.markSkipped("User declined approval");
                    return;
                }
            }
            
            // Execute based on action type
            String result = executeStepByType(step);
            step.markCompleted(result);
            
            logger.debug("Step " + step.getStepNumber() + " completed: " + result);
            
        } catch (Exception e) {
            step.markFailed(e.getMessage());
            throw e;
        }
    }
    
    private String executeStepByType(ExecutionStep step) throws Exception {
        String actionType = step.getActionType().toLowerCase();
        String operation = step.getOperation();
        
        switch (actionType) {
            case "filesystem":
                return executeFileSystemOperation(operation);
                
            case "editor":
                return executeEditorOperation(operation);
                
            case "terminal":
                return executeTerminalOperation(operation);
                
            case "general":
            default:
                return executeGeneralOperation(operation);
        }
    }
    
    private String executeFileSystemOperation(String operation) throws Exception {
        // Parse operation and execute appropriate file system action
        String lowerOp = operation.toLowerCase();
        
        if (lowerOp.contains("read file")) {
            String filePath = extractFilePath(operation);
            String content = fileSystemAdapter.readFile(filePath);
            return "Read file: " + filePath + " (" + content.length() + " characters)";
            
        } else if (lowerOp.contains("write file") || lowerOp.contains("create file")) {
            // This would need more sophisticated parsing to extract file path and content
            return "File operation completed";
            
        } else if (lowerOp.contains("delete file")) {
            String filePath = extractFilePath(operation);
            fileSystemAdapter.deleteFile(filePath);
            return "Deleted file: " + filePath;
            
        } else if (lowerOp.contains("list directory")) {
            String dirPath = extractDirectoryPath(operation);
            java.util.List<String> items = fileSystemAdapter.listDirectory(dirPath);
            return "Listed directory: " + dirPath + " (" + items.size() + " items)";

        } else {
            return "File system operation: " + operation;
        }
    }
    
    private String executeEditorOperation(String operation) throws Exception {
        String lowerOp = operation.toLowerCase();
        
        if (lowerOp.contains("open file")) {
            String filePath = extractFilePath(operation);
            boolean success = editorAdapter.openFile(filePath);
            return success ? "Opened file: " + filePath : "Failed to open file: " + filePath;
            
        } else if (lowerOp.contains("get content")) {
            String content = editorAdapter.getActiveEditorContent();
            return "Retrieved editor content (" + (content != null ? content.length() : 0) + " characters)";
            
        } else if (lowerOp.contains("get selection")) {
            String selection = editorAdapter.getSelectedText();
            return "Retrieved selection: " + (selection != null ? selection : "none");
            
        } else if (lowerOp.contains("save")) {
            boolean success = editorAdapter.saveActiveEditor();
            return success ? "Saved active editor" : "Failed to save active editor";
            
        } else {
            return "Editor operation: " + operation;
        }
    }
    
    private String executeTerminalOperation(String operation) throws Exception {
        // Extract command from operation
        String command = extractCommand(operation);
        
        TerminalAdapter.CommandResult result = terminalAdapter.executeCommand(
            command, 
            contextManager.getCurrentWorkingDirectory(), 
            60000 // 1 minute timeout
        );
        
        if (result.isSuccess()) {
            return "Command executed successfully: " + command + "\nOutput: " + result.getOutput();
        } else {
            throw new Exception("Command failed: " + command + "\nError: " + result.getErrorMessage());
        }
    }
    
    private String executeGeneralOperation(String operation) throws Exception {
        // For general operations, we might need to use AI to determine the specific action
        logger.info("Executing general operation: " + operation);
        return "General operation completed: " + operation;
    }
    
    private String extractFilePath(String operation) {
        // Simple extraction - look for quoted paths or common patterns
        if (operation.contains("'")) {
            int start = operation.indexOf("'") + 1;
            int end = operation.indexOf("'", start);
            if (end > start) {
                return operation.substring(start, end);
            }
        }
        
        // Fallback - return the operation itself
        return operation;
    }
    
    private String extractDirectoryPath(String operation) {
        return extractFilePath(operation); // Same logic for now
    }
    
    private String extractCommand(String operation) {
        // Look for common command patterns
        String[] prefixes = {"run ", "execute ", "command ", "terminal "};
        
        for (String prefix : prefixes) {
            if (operation.toLowerCase().startsWith(prefix)) {
                return operation.substring(prefix.length()).trim();
            }
        }
        
        return operation;
    }
    
    private boolean shouldRequireApproval() {
        return AIAgentPreferences.getPreferenceStore()
            .getBoolean(AIAgentPreferences.PREF_REQUIRE_APPROVAL);
    }
    
    private boolean requestUserApproval(ExecutionStep step) {
        // TODO: Implement user approval dialog
        // For now, return true (auto-approve) in development
        logger.info("Requesting user approval for step: " + step.getOperation());
        return true;
    }
    
    private void handleStepFailure(ExecutionStep step, ExecutionPlan plan) {
        logger.warn("Step " + step.getStepNumber() + " failed: " + step.getErrorMessage());
        
        // Notify feedback loop if available
        if (feedbackLoop != null) {
            try {
                feedbackLoop.handleStepFailure(step, plan);
            } catch (Exception e) {
                logger.error("Error in feedback loop handling step failure", e);
            }
        }
    }
    
    /**
     * Cancel the current execution
     */
    public void cancelCurrentExecution() {
        if (executing.get()) {
            logger.info("Cancelling current execution");
            cancelled.set(true);
        }
    }
    
    /**
     * Get the current execution status
     */
    public String getCurrentStatus() {
        if (!executing.get()) {
            return "Idle";
        }
        
        if (currentPlan != null) {
            ExecutionStep currentStep = currentPlan.getCurrentStep();
            if (currentStep != null) {
                return "Executing step " + currentStep.getStepNumber() + ": " + currentStep.getOperation();
            }
        }
        
        return "Executing";
    }
    
    /**
     * Check if execution is currently in progress
     */
    public boolean isExecuting() {
        return executing.get();
    }
    
    /**
     * Get the current execution plan
     */
    public ExecutionPlan getCurrentPlan() {
        return currentPlan;
    }
    
    /**
     * Shutdown the execution orchestrator
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        logger.info("Shutting down ExecutionOrchestrator...");
        
        cancelCurrentExecution();
        currentPlan = null;
        initialized = false;
        
        logger.info("ExecutionOrchestrator shut down successfully");
    }
}
