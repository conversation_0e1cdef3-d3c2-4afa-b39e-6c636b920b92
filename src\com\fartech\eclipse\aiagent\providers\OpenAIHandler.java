package com.fartech.eclipse.aiagent.providers;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * OpenAI API handler implementation.
 */
public class OpenAIHandler implements AIProviderHandler {
    
    private final Gson gson;
    private final AIAgentLogger logger;
    private final JsonParser jsonParser;
    
    public OpenAIHandler(<PERSON><PERSON> gson, AIAgentLogger logger) {
        this.gson = gson;
        this.logger = logger;
        this.jsonParser = new JsonParser();
    }
    
    @Override
    public HttpPost buildHttpRequest(String endpoint, String apiKey, AIRequest request) throws AIProviderException {
        try {
            HttpPost httpPost = new HttpPost(endpoint);
            
            // Set headers
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", "Bearer " + apiKey);
            
            // Build request body
            Map<String, Object> requestBody = new HashMap<String, Object>();
            
            // Get model from preferences
            String model = AIAgentPreferences.getPreferenceStore()
                .getString(AIAgentPreferences.PREF_AI_PROVIDER_MODEL);
            if (model == null || model.trim().isEmpty()) {
                model = getDefaultModel();
            }
            requestBody.put("model", model);
            
            // Build messages array
            List<Map<String, String>> messages = new ArrayList<Map<String, String>>();
            
            // Add system message if present
            if (request.getSystemMessage() != null && !request.getSystemMessage().trim().isEmpty()) {
                Map<String, String> systemMessage = new HashMap<String, String>();
                systemMessage.put("role", "system");
                systemMessage.put("content", request.getSystemMessage());
                messages.add(systemMessage);
            }
            
            // Add context as assistant messages if present
            if (request.getContext() != null && !request.getContext().isEmpty()) {
                for (String contextItem : request.getContext()) {
                    Map<String, String> contextMessage = new HashMap<String, String>();
                    contextMessage.put("role", "assistant");
                    contextMessage.put("content", "Context: " + contextItem);
                    messages.add(contextMessage);
                }
            }
            
            // Add user message
            Map<String, String> userMessage = new HashMap<String, String>();
            userMessage.put("role", "user");
            userMessage.put("content", request.getPrompt());
            messages.add(userMessage);
            
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", request.getMaxTokens());
            requestBody.put("temperature", request.getTemperature());
            requestBody.put("stream", request.isStream());
            
            // Convert to JSON
            String jsonBody = gson.toJson(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, "UTF-8"));
            
            logger.debug("Built OpenAI request for model: " + model);
            return httpPost;
            
        } catch (Exception e) {
            throw new AIProviderException("Failed to build OpenAI request", getProviderType(), e);
        }
    }
    
    @Override
    public AIResponse processResponse(HttpResponse response) throws AIProviderException {
        long startTime = System.currentTimeMillis();
        
        try {
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (statusCode != 200) {
                return handleErrorResponse(statusCode, responseBody);
            }
            
            // Parse successful response
            JsonObject jsonResponse = jsonParser.parse(responseBody).getAsJsonObject();
            
            AIResponse aiResponse = new AIResponse();
            aiResponse.setSuccess(true);
            aiResponse.setResponseTimeMs(responseTime);
            
            // Extract model
            if (jsonResponse.has("model")) {
                aiResponse.setModel(jsonResponse.get("model").getAsString());
            }
            
            // Extract usage information
            if (jsonResponse.has("usage")) {
                JsonObject usage = jsonResponse.getAsJsonObject("usage");
                if (usage.has("total_tokens")) {
                    aiResponse.setTokensUsed(usage.get("total_tokens").getAsInt());
                }
            }
            
            // Extract content from choices
            if (jsonResponse.has("choices")) {
                JsonArray choices = jsonResponse.getAsJsonArray("choices");
                if (choices.size() > 0) {
                    JsonObject firstChoice = choices.get(0).getAsJsonObject();
                    
                    // Extract finish reason
                    if (firstChoice.has("finish_reason")) {
                        JsonElement finishReason = firstChoice.get("finish_reason");
                        if (!finishReason.isJsonNull()) {
                            aiResponse.setFinishReason(finishReason.getAsString());
                        }
                    }
                    
                    // Extract message content
                    if (firstChoice.has("message")) {
                        JsonObject message = firstChoice.getAsJsonObject("message");
                        if (message.has("content")) {
                            aiResponse.setContent(message.get("content").getAsString());
                        }
                    }
                }
            }
            
            if (!aiResponse.hasContent()) {
                throw new AIProviderException("No content in OpenAI response", getProviderType());
            }
            
            logger.debug("Processed OpenAI response successfully");
            return aiResponse;
            
        } catch (IOException e) {
            throw new AIProviderException("Failed to read OpenAI response", getProviderType(), e);
        } catch (Exception e) {
            throw new AIProviderException("Failed to process OpenAI response", getProviderType(), e);
        }
    }
    
    private AIResponse handleErrorResponse(int statusCode, String responseBody) throws AIProviderException {
        try {
            JsonObject errorJson = jsonParser.parse(responseBody).getAsJsonObject();
            String errorMessage = "OpenAI API error";
            String errorCode = null;
            
            if (errorJson.has("error")) {
                JsonObject error = errorJson.getAsJsonObject("error");
                if (error.has("message")) {
                    errorMessage = error.get("message").getAsString();
                }
                if (error.has("code")) {
                    errorCode = error.get("code").getAsString();
                }
            }
            
            throw new AIProviderException(errorMessage, getProviderType(), statusCode, errorCode);
            
        } catch (Exception e) {
            // If we can't parse the error, return a generic message
            String message = "OpenAI API error (HTTP " + statusCode + ")";
            if (responseBody != null && !responseBody.trim().isEmpty()) {
                message += ": " + responseBody;
            }
            throw new AIProviderException(message, getProviderType(), statusCode);
        }
    }
    
    @Override
    public String getProviderType() {
        return "openai";
    }
    
    @Override
    public boolean validateApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = apiKey.trim();
        
        // OpenAI API keys typically start with "sk-" and are 51 characters long
        return trimmed.startsWith("sk-") && trimmed.length() >= 40;
    }
    
    @Override
    public String getDefaultModel() {
        return "gpt-4";
    }
    
    @Override
    public int getMaxTokens() {
        return 8192; // Conservative default for GPT-4
    }
}
