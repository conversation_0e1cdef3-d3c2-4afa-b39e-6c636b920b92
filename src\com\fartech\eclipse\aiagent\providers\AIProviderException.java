package com.fartech.eclipse.aiagent.providers;

/**
 * Exception thrown when AI provider operations fail.
 */
public class AIProviderException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private final String providerType;
    private final int httpStatusCode;
    private final String providerErrorCode;
    
    public AIProviderException(String message) {
        super(message);
        this.providerType = null;
        this.httpStatusCode = -1;
        this.providerErrorCode = null;
    }
    
    public AIProviderException(String message, Throwable cause) {
        super(message, cause);
        this.providerType = null;
        this.httpStatusCode = -1;
        this.providerErrorCode = null;
    }
    
    public AIProviderException(String message, String providerType) {
        super(message);
        this.providerType = providerType;
        this.httpStatusCode = -1;
        this.providerErrorCode = null;
    }
    
    public AIProviderException(String message, String providerType, int httpStatusCode) {
        super(message);
        this.providerType = providerType;
        this.httpStatusCode = httpStatusCode;
        this.providerErrorCode = null;
    }
    
    public AIProviderException(String message, String providerType, int httpStatusCode, String providerErrorCode) {
        super(message);
        this.providerType = providerType;
        this.httpStatusCode = httpStatusCode;
        this.providerErrorCode = providerErrorCode;
    }
    
    public AIProviderException(String message, String providerType, Throwable cause) {
        super(message, cause);
        this.providerType = providerType;
        this.httpStatusCode = -1;
        this.providerErrorCode = null;
    }
    
    public String getProviderType() {
        return providerType;
    }
    
    public int getHttpStatusCode() {
        return httpStatusCode;
    }
    
    public String getProviderErrorCode() {
        return providerErrorCode;
    }
    
    public boolean hasHttpStatusCode() {
        return httpStatusCode > 0;
    }
    
    public boolean hasProviderErrorCode() {
        return providerErrorCode != null && !providerErrorCode.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AIProviderException: ").append(getMessage());
        
        if (providerType != null) {
            sb.append(" [Provider: ").append(providerType).append("]");
        }
        
        if (hasHttpStatusCode()) {
            sb.append(" [HTTP: ").append(httpStatusCode).append("]");
        }
        
        if (hasProviderErrorCode()) {
            sb.append(" [Error Code: ").append(providerErrorCode).append("]");
        }
        
        return sb.toString();
    }
}
