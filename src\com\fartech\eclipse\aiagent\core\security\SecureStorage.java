package com.fartech.eclipse.aiagent.core.security;

import org.eclipse.equinox.security.storage.ISecurePreferences;
import org.eclipse.equinox.security.storage.SecurePreferencesFactory;
import org.eclipse.equinox.security.storage.StorageException;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Secure storage utility for managing sensitive data like API keys.
 * Uses Eclipse's SecurePreferences for encrypted storage.
 */
public class SecureStorage {
    
    private static final String NODE_NAME = "com.fartech.eclipse.aiagent";
    private static final String API_KEY_PREFIX = "api.key.";
    
    private final ISecurePreferences securePrefs;
    private final AIAgentLogger logger;
    
    public SecureStorage() {
        this.securePrefs = SecurePreferencesFactory.getDefault().node(NODE_NAME);
        this.logger = Activator.getDefault().getLogger();
    }
    
    /**
     * Store an API key securely
     * @param providerType the AI provider type (e.g., "openai", "anthropic")
     * @param apiKey the API key to store
     * @throws SecurityException if storage fails
     */
    public void storeApiKey(String providerType, String apiKey) throws SecurityException {
        if (providerType == null || providerType.trim().isEmpty()) {
            throw new IllegalArgumentException("Provider type cannot be null or empty");
        }
        
        if (apiKey == null) {
            throw new IllegalArgumentException("API key cannot be null");
        }
        
        try {
            String key = API_KEY_PREFIX + providerType.toLowerCase();
            securePrefs.put(key, apiKey, true); // true = encrypt
            securePrefs.flush();
            
            logger.info("API key stored securely for provider: " + providerType);
        } catch (StorageException e) {
            logger.error("Failed to store API key for provider: " + providerType, e);
            throw new SecurityException("Failed to store API key securely", e);
        } catch (Exception e) {
            logger.error("Unexpected error storing API key for provider: " + providerType, e);
            throw new SecurityException("Unexpected error during secure storage", e);
        }
    }
    
    /**
     * Retrieve an API key securely
     * @param providerType the AI provider type
     * @return the API key, or null if not found
     * @throws SecurityException if retrieval fails
     */
    public String getApiKey(String providerType) throws SecurityException {
        if (providerType == null || providerType.trim().isEmpty()) {
            throw new IllegalArgumentException("Provider type cannot be null or empty");
        }
        
        try {
            String key = API_KEY_PREFIX + providerType.toLowerCase();
            String apiKey = securePrefs.get(key, null);
            
            if (apiKey != null) {
                logger.debug("API key retrieved for provider: " + providerType);
            } else {
                logger.debug("No API key found for provider: " + providerType);
            }
            
            return apiKey;
        } catch (StorageException e) {
            logger.error("Failed to retrieve API key for provider: " + providerType, e);
            throw new SecurityException("Failed to retrieve API key securely", e);
        } catch (Exception e) {
            logger.error("Unexpected error retrieving API key for provider: " + providerType, e);
            throw new SecurityException("Unexpected error during secure retrieval", e);
        }
    }
    
    /**
     * Remove an API key from secure storage
     * @param providerType the AI provider type
     * @throws SecurityException if removal fails
     */
    public void removeApiKey(String providerType) throws SecurityException {
        if (providerType == null || providerType.trim().isEmpty()) {
            throw new IllegalArgumentException("Provider type cannot be null or empty");
        }
        
        try {
            String key = API_KEY_PREFIX + providerType.toLowerCase();
            securePrefs.remove(key);
            securePrefs.flush();
            
            logger.info("API key removed for provider: " + providerType);
        } catch (Exception e) {
            logger.error("Failed to remove API key for provider: " + providerType, e);
            throw new SecurityException("Failed to remove API key securely", e);
        }
    }
    
    /**
     * Check if an API key exists for the given provider
     * @param providerType the AI provider type
     * @return true if an API key exists, false otherwise
     */
    public boolean hasApiKey(String providerType) {
        if (providerType == null || providerType.trim().isEmpty()) {
            return false;
        }
        
        try {
            String key = API_KEY_PREFIX + providerType.toLowerCase();
            String apiKey = securePrefs.get(key, null);
            return apiKey != null && !apiKey.trim().isEmpty();
        } catch (Exception e) {
            logger.warn("Error checking for API key existence for provider: " + providerType, e);
            return false;
        }
    }
    
    /**
     * Clear all stored API keys (use with caution)
     * @throws SecurityException if clearing fails
     */
    public void clearAllApiKeys() throws SecurityException {
        try {
            String[] keys = securePrefs.keys();
            for (String key : keys) {
                if (key.startsWith(API_KEY_PREFIX)) {
                    securePrefs.remove(key);
                }
            }
            securePrefs.flush();
            
            logger.info("All API keys cleared from secure storage");
        } catch (Exception e) {
            logger.error("Failed to clear API keys from secure storage", e);
            throw new SecurityException("Failed to clear API keys securely", e);
        }
    }
    
    /**
     * Validate that an API key meets basic security requirements
     * @param apiKey the API key to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        // Basic validation - adjust based on provider requirements
        String trimmed = apiKey.trim();
        
        // Minimum length check
        if (trimmed.length() < 10) {
            return false;
        }
        
        // Check for obvious test/placeholder values
        String lower = trimmed.toLowerCase();
        if (lower.contains("test") || lower.contains("example") || 
            lower.contains("placeholder") || lower.equals("your-api-key")) {
            return false;
        }
        
        return true;
    }
}
