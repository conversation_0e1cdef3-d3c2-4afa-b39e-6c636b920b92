package com.fartech.eclipse.aiagent.providers.models;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a request to an AI provider.
 */
public class AIRequest {
    
    private String prompt;
    private List<String> context;
    private int maxTokens;
    private double temperature;
    private String systemMessage;
    private boolean stream;
    
    public AIRequest(String prompt) {
        this(prompt, 4000, 0.7);
    }
    
    public AIRequest(String prompt, int maxTokens, double temperature) {
        this.prompt = prompt;
        this.maxTokens = maxTokens;
        this.temperature = temperature;
        this.context = new ArrayList<String>();
        this.stream = false;
    }
    
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    public List<String> getContext() {
        return context;
    }
    
    public void setContext(List<String> context) {
        this.context = context != null ? context : new ArrayList<String>();
    }
    
    public void addContext(String contextItem) {
        if (contextItem != null && !contextItem.trim().isEmpty()) {
            this.context.add(contextItem);
        }
    }
    
    public int getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }
    
    public String getSystemMessage() {
        return systemMessage;
    }
    
    public void setSystemMessage(String systemMessage) {
        this.systemMessage = systemMessage;
    }
    
    public boolean isStream() {
        return stream;
    }
    
    public void setStream(boolean stream) {
        this.stream = stream;
    }
    
    /**
     * Build the complete prompt including context and system message
     */
    public String buildCompletePrompt() {
        StringBuilder builder = new StringBuilder();
        
        // Add system message if present
        if (systemMessage != null && !systemMessage.trim().isEmpty()) {
            builder.append("System: ").append(systemMessage).append("\n\n");
        }
        
        // Add context if present
        if (context != null && !context.isEmpty()) {
            builder.append("Context:\n");
            for (String contextItem : context) {
                builder.append("- ").append(contextItem).append("\n");
            }
            builder.append("\n");
        }
        
        // Add main prompt
        builder.append("User: ").append(prompt);
        
        return builder.toString();
    }
    
    @Override
    public String toString() {
        return "AIRequest{" +
                "prompt='" + (prompt != null ? prompt.substring(0, Math.min(50, prompt.length())) + "..." : "null") + '\'' +
                ", contextItems=" + (context != null ? context.size() : 0) +
                ", maxTokens=" + maxTokens +
                ", temperature=" + temperature +
                ", systemMessage='" + (systemMessage != null ? "present" : "null") + '\'' +
                ", stream=" + stream +
                '}';
    }
}
