package com.fartech.eclipse.aiagent.core.preferences;

import org.eclipse.core.runtime.preferences.AbstractPreferenceInitializer;
import org.eclipse.jface.preference.IPreferenceStore;

import com.fartech.eclipse.aiagent.Activator;

/**
 * Preference constants and default values for the AI Agent plugin.
 */
public class AIAgentPreferences extends AbstractPreferenceInitializer {
    
    // AI Provider Configuration
    public static final String PREF_AI_PROVIDER_TYPE = "ai.provider.type";
    public static final String PREF_AI_PROVIDER_ENDPOINT = "ai.provider.endpoint";
    public static final String PREF_AI_PROVIDER_MODEL = "ai.provider.model";
    public static final String PREF_AI_PROVIDER_TIMEOUT = "ai.provider.timeout";
    public static final String PREF_AI_PROVIDER_MAX_TOKENS = "ai.provider.maxTokens";
    public static final String PREF_AI_PROVIDER_TEMPERATURE = "ai.provider.temperature";
    
    // Security Settings
    public static final String PREF_REQUIRE_APPROVAL = "security.requireApproval";
    public static final String PREF_ENABLE_DATA_SCRUBBING = "security.enableDataScrubbing";
    public static final String PREF_SENSITIVE_PATTERNS = "security.sensitivePatterns";
    
    // Agent Behavior
    public static final String PREF_ENABLE_AUTONOMOUS_MODE = "agent.enableAutonomousMode";
    public static final String PREF_MAX_EXECUTION_STEPS = "agent.maxExecutionSteps";
    public static final String PREF_ENABLE_SELF_CORRECTION = "agent.enableSelfCorrection";
    public static final String PREF_MAX_RETRY_ATTEMPTS = "agent.maxRetryAttempts";
    
    // UI Settings
    public static final String PREF_SHOW_PROGRESS_DETAILS = "ui.showProgressDetails";
    public static final String PREF_AUTO_OPEN_DIFF_VIEW = "ui.autoOpenDiffView";
    public static final String PREF_ENABLE_SYNTAX_HIGHLIGHTING = "ui.enableSyntaxHighlighting";
    
    // Logging
    public static final String PREF_LOG_LEVEL = "logging.level";
    public static final String PREF_ENABLE_CONSOLE_LOGGING = "logging.enableConsole";
    public static final String PREF_LOG_AI_INTERACTIONS = "logging.logAIInteractions";
    
    // Default values
    public static final String DEFAULT_AI_PROVIDER_TYPE = "openai";
    public static final String DEFAULT_AI_PROVIDER_ENDPOINT = "https://api.openai.com/v1/chat/completions";
    public static final String DEFAULT_AI_PROVIDER_MODEL = "gpt-4";
    public static final int DEFAULT_AI_PROVIDER_TIMEOUT = 30000; // 30 seconds
    public static final int DEFAULT_AI_PROVIDER_MAX_TOKENS = 4000;
    public static final double DEFAULT_AI_PROVIDER_TEMPERATURE = 0.7;
    
    public static final boolean DEFAULT_REQUIRE_APPROVAL = true;
    public static final boolean DEFAULT_ENABLE_DATA_SCRUBBING = false;
    public static final String DEFAULT_SENSITIVE_PATTERNS = "password,secret,key,token,credential";
    
    public static final boolean DEFAULT_ENABLE_AUTONOMOUS_MODE = true;
    public static final int DEFAULT_MAX_EXECUTION_STEPS = 10;
    public static final boolean DEFAULT_ENABLE_SELF_CORRECTION = true;
    public static final int DEFAULT_MAX_RETRY_ATTEMPTS = 3;
    
    public static final boolean DEFAULT_SHOW_PROGRESS_DETAILS = true;
    public static final boolean DEFAULT_AUTO_OPEN_DIFF_VIEW = true;
    public static final boolean DEFAULT_ENABLE_SYNTAX_HIGHLIGHTING = true;
    
    public static final String DEFAULT_LOG_LEVEL = "INFO";
    public static final boolean DEFAULT_ENABLE_CONSOLE_LOGGING = true;
    public static final boolean DEFAULT_LOG_AI_INTERACTIONS = false;
    
    @Override
    public void initializeDefaultPreferences() {
        IPreferenceStore store = Activator.getDefault().getPreferenceStore();
        
        // AI Provider Configuration
        store.setDefault(PREF_AI_PROVIDER_TYPE, DEFAULT_AI_PROVIDER_TYPE);
        store.setDefault(PREF_AI_PROVIDER_ENDPOINT, DEFAULT_AI_PROVIDER_ENDPOINT);
        store.setDefault(PREF_AI_PROVIDER_MODEL, DEFAULT_AI_PROVIDER_MODEL);
        store.setDefault(PREF_AI_PROVIDER_TIMEOUT, DEFAULT_AI_PROVIDER_TIMEOUT);
        store.setDefault(PREF_AI_PROVIDER_MAX_TOKENS, DEFAULT_AI_PROVIDER_MAX_TOKENS);
        store.setDefault(PREF_AI_PROVIDER_TEMPERATURE, DEFAULT_AI_PROVIDER_TEMPERATURE);
        
        // Security Settings
        store.setDefault(PREF_REQUIRE_APPROVAL, DEFAULT_REQUIRE_APPROVAL);
        store.setDefault(PREF_ENABLE_DATA_SCRUBBING, DEFAULT_ENABLE_DATA_SCRUBBING);
        store.setDefault(PREF_SENSITIVE_PATTERNS, DEFAULT_SENSITIVE_PATTERNS);
        
        // Agent Behavior
        store.setDefault(PREF_ENABLE_AUTONOMOUS_MODE, DEFAULT_ENABLE_AUTONOMOUS_MODE);
        store.setDefault(PREF_MAX_EXECUTION_STEPS, DEFAULT_MAX_EXECUTION_STEPS);
        store.setDefault(PREF_ENABLE_SELF_CORRECTION, DEFAULT_ENABLE_SELF_CORRECTION);
        store.setDefault(PREF_MAX_RETRY_ATTEMPTS, DEFAULT_MAX_RETRY_ATTEMPTS);
        
        // UI Settings
        store.setDefault(PREF_SHOW_PROGRESS_DETAILS, DEFAULT_SHOW_PROGRESS_DETAILS);
        store.setDefault(PREF_AUTO_OPEN_DIFF_VIEW, DEFAULT_AUTO_OPEN_DIFF_VIEW);
        store.setDefault(PREF_ENABLE_SYNTAX_HIGHLIGHTING, DEFAULT_ENABLE_SYNTAX_HIGHLIGHTING);
        
        // Logging
        store.setDefault(PREF_LOG_LEVEL, DEFAULT_LOG_LEVEL);
        store.setDefault(PREF_ENABLE_CONSOLE_LOGGING, DEFAULT_ENABLE_CONSOLE_LOGGING);
        store.setDefault(PREF_LOG_AI_INTERACTIONS, DEFAULT_LOG_AI_INTERACTIONS);
    }
    
    /**
     * Get the preference store for the AI Agent plugin
     */
    public static IPreferenceStore getPreferenceStore() {
        return Activator.getDefault().getPreferenceStore();
    }
}
