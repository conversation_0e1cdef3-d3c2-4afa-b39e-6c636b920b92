package com.fartech.eclipse.aiagent.ui.views;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.part.ViewPart;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * View for reviewing and approving proposed changes.
 */
public class DiffApprovalView extends ViewPart {
    
    public static final String ID = "com.fartech.eclipse.aiagent.ui.views.DiffApprovalView";
    
    private AIAgentLogger logger;
    
    private Label titleLabel;
    private StyledText diffText;
    private Button approveButton;
    private Button rejectButton;
    private Button modifyButton;
    private Label statusLabel;
    
    private PendingChange currentChange;
    
    @Override
    public void createPartControl(Composite parent) {
        logger = Activator.getDefault().getLogger();
        
        // Create main container
        Composite container = new Composite(parent, SWT.NONE);
        container.setLayout(new GridLayout(1, false));
        
        // Create header area
        createHeaderArea(container);
        
        // Create diff display area
        createDiffArea(container);
        
        // Create button area
        createButtonArea(container);
        
        // Create status area
        createStatusArea(container);
        
        // Create actions
        createActions();
        
        // Initialize with no pending changes
        updateDisplay(null);
        
        logger.info("DiffApprovalView created successfully");
    }
    
    private void createHeaderArea(Composite parent) {
        Composite headerComposite = new Composite(parent, SWT.NONE);
        headerComposite.setLayout(new GridLayout(1, false));
        headerComposite.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        titleLabel = new Label(headerComposite, SWT.WRAP);
        titleLabel.setText("No pending changes");
        titleLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
    }
    
    private void createDiffArea(Composite parent) {
        Composite diffComposite = new Composite(parent, SWT.NONE);
        diffComposite.setLayout(new GridLayout(1, false));
        diffComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        Label diffLabel = new Label(diffComposite, SWT.NONE);
        diffLabel.setText("Proposed Changes:");
        diffLabel.setLayoutData(new GridData(SWT.LEFT, SWT.TOP, false, false));
        
        diffText = new StyledText(diffComposite, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL | SWT.READ_ONLY);
        diffText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        diffText.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
        diffText.setText("No changes to review");
    }
    
    private void createButtonArea(Composite parent) {
        Composite buttonComposite = new Composite(parent, SWT.NONE);
        buttonComposite.setLayout(new GridLayout(3, false));
        buttonComposite.setLayoutData(new GridData(SWT.CENTER, SWT.TOP, false, false));
        
        approveButton = new Button(buttonComposite, SWT.PUSH);
        approveButton.setText("Approve");
        approveButton.setEnabled(false);
        approveButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                approveChange();
            }
        });
        
        rejectButton = new Button(buttonComposite, SWT.PUSH);
        rejectButton.setText("Reject");
        rejectButton.setEnabled(false);
        rejectButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                rejectChange();
            }
        });
        
        modifyButton = new Button(buttonComposite, SWT.PUSH);
        modifyButton.setText("Modify");
        modifyButton.setEnabled(false);
        modifyButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                modifyChange();
            }
        });
    }
    
    private void createStatusArea(Composite parent) {
        statusLabel = new Label(parent, SWT.NONE);
        statusLabel.setText("Ready");
        statusLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
    }
    
    private void createActions() {
        IMenuManager menuManager = getViewSite().getActionBars().getMenuManager();
        IToolBarManager toolBarManager = getViewSite().getActionBars().getToolBarManager();
        
        // Refresh action
        Action refreshAction = new Action("Refresh") {
            @Override
            public void run() {
                refreshPendingChanges();
            }
        };
        refreshAction.setToolTipText("Refresh pending changes");
        
        // Clear action
        Action clearAction = new Action("Clear") {
            @Override
            public void run() {
                clearDisplay();
            }
        };
        clearAction.setToolTipText("Clear the current display");
        
        // Add to menu and toolbar
        menuManager.add(refreshAction);
        menuManager.add(clearAction);
        
        toolBarManager.add(refreshAction);
        toolBarManager.add(clearAction);
    }
    
    /**
     * Display a pending change for approval
     * @param change the pending change to display
     */
    public void displayPendingChange(PendingChange change) {
        this.currentChange = change;
        updateDisplay(change);
    }
    
    private void updateDisplay(PendingChange change) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (change != null) {
                    titleLabel.setText("Pending Change: " + change.getDescription());
                    diffText.setText(change.getDiffContent());
                    
                    approveButton.setEnabled(true);
                    rejectButton.setEnabled(true);
                    modifyButton.setEnabled(true);
                    
                    statusLabel.setText("Waiting for approval");
                } else {
                    titleLabel.setText("No pending changes");
                    diffText.setText("No changes to review");
                    
                    approveButton.setEnabled(false);
                    rejectButton.setEnabled(false);
                    modifyButton.setEnabled(false);
                    
                    statusLabel.setText("Ready");
                }
            }
        });
    }
    
    private void approveChange() {
        if (currentChange != null) {
            logger.info("User approved change: " + currentChange.getDescription());
            
            try {
                currentChange.approve();
                statusLabel.setText("Change approved and applied");
                
                // Clear the display after a short delay
                Display.getCurrent().timerExec(2000, new Runnable() {
                    @Override
                    public void run() {
                        clearDisplay();
                    }
                });
                
            } catch (Exception e) {
                logger.error("Error applying approved change", e);
                statusLabel.setText("Error applying change: " + e.getMessage());
            }
        }
    }
    
    private void rejectChange() {
        if (currentChange != null) {
            logger.info("User rejected change: " + currentChange.getDescription());
            
            try {
                currentChange.reject();
                statusLabel.setText("Change rejected");
                
                // Clear the display after a short delay
                Display.getCurrent().timerExec(2000, new Runnable() {
                    @Override
                    public void run() {
                        clearDisplay();
                    }
                });
                
            } catch (Exception e) {
                logger.error("Error handling rejected change", e);
                statusLabel.setText("Error handling rejection: " + e.getMessage());
            }
        }
    }
    
    private void modifyChange() {
        if (currentChange != null) {
            logger.info("User requested modification of change: " + currentChange.getDescription());
            
            // TODO: Implement modification dialog
            statusLabel.setText("Modification not yet implemented");
        }
    }
    
    private void refreshPendingChanges() {
        // TODO: Check for pending changes from the AI Agent Core
        logger.debug("Refreshing pending changes");
    }
    
    private void clearDisplay() {
        currentChange = null;
        updateDisplay(null);
    }
    
    @Override
    public void setFocus() {
        if (diffText != null && !diffText.isDisposed()) {
            diffText.setFocus();
        }
    }
    
    /**
     * Represents a pending change that requires user approval
     */
    public static class PendingChange {
        private final String description;
        private final String diffContent;
        private final Runnable approveAction;
        private final Runnable rejectAction;
        
        public PendingChange(String description, String diffContent, Runnable approveAction, Runnable rejectAction) {
            this.description = description;
            this.diffContent = diffContent;
            this.approveAction = approveAction;
            this.rejectAction = rejectAction;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getDiffContent() {
            return diffContent;
        }
        
        public void approve() throws Exception {
            if (approveAction != null) {
                approveAction.run();
            }
        }
        
        public void reject() throws Exception {
            if (rejectAction != null) {
                rejectAction.run();
            }
        }
    }
}
