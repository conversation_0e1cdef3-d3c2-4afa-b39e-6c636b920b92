package com.fartech.eclipse.aiagent.ui.views;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IMenuManager;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.jface.viewers.ArrayContentProvider;
import org.eclipse.jface.viewers.ColumnLabelProvider;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TableViewerColumn;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.ProgressBar;
import org.eclipse.swt.widgets.Table;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.part.ViewPart;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.AIAgentCore;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.planning.ExecutionPlan;
import com.fartech.eclipse.aiagent.core.planning.ExecutionStep;

/**
 * Progress view for monitoring AI Agent execution.
 */
public class ProgressView extends ViewPart {
    
    public static final String ID = "com.fartech.eclipse.aiagent.ui.views.ProgressView";
    
    private AIAgentLogger logger;
    private AIAgentCore aiAgentCore;
    
    private Label goalLabel;
    private ProgressBar progressBar;
    private Label progressLabel;
    private TableViewer stepsTableViewer;
    private Text detailsText;
    
    private ExecutionPlan currentPlan;
    
    @Override
    public void createPartControl(Composite parent) {
        logger = Activator.getDefault().getLogger();
        aiAgentCore = Activator.getDefault().getAIAgentCore();
        
        // Create main container
        Composite container = new Composite(parent, SWT.NONE);
        container.setLayout(new GridLayout(1, false));
        
        // Create header area
        createHeaderArea(container);
        
        // Create sash form for main content
        SashForm sashForm = new SashForm(container, SWT.VERTICAL);
        sashForm.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        // Create steps table
        createStepsTable(sashForm);
        
        // Create details area
        createDetailsArea(sashForm);
        
        // Set sash weights (60% table, 40% details)
        sashForm.setWeights(new int[] { 60, 40 });
        
        // Create actions
        createActions();
        
        // Start monitoring
        startProgressMonitoring();
        
        logger.info("ProgressView created successfully");
    }
    
    private void createHeaderArea(Composite parent) {
        Composite headerComposite = new Composite(parent, SWT.NONE);
        headerComposite.setLayout(new GridLayout(1, false));
        headerComposite.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        // Goal label
        goalLabel = new Label(headerComposite, SWT.WRAP);
        goalLabel.setText("No active task");
        goalLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        // Progress bar
        progressBar = new ProgressBar(headerComposite, SWT.HORIZONTAL);
        progressBar.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        progressBar.setMinimum(0);
        progressBar.setMaximum(100);
        
        // Progress label
        progressLabel = new Label(headerComposite, SWT.NONE);
        progressLabel.setText("0% complete");
        progressLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
    }
    
    private void createStepsTable(Composite parent) {
        Composite tableComposite = new Composite(parent, SWT.NONE);
        tableComposite.setLayout(new GridLayout(1, false));
        
        Label tableLabel = new Label(tableComposite, SWT.NONE);
        tableLabel.setText("Execution Steps:");
        tableLabel.setLayoutData(new GridData(SWT.LEFT, SWT.TOP, false, false));
        
        stepsTableViewer = new TableViewer(tableComposite, SWT.BORDER | SWT.FULL_SELECTION);
        Table table = stepsTableViewer.getTable();
        table.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        table.setHeaderVisible(true);
        table.setLinesVisible(true);
        
        // Create columns
        createTableColumns();
        
        // Set content provider
        stepsTableViewer.setContentProvider(ArrayContentProvider.getInstance());
        
        // Add selection listener
        stepsTableViewer.addSelectionChangedListener(event -> {
            updateDetailsArea();
        });
    }
    
    private void createTableColumns() {
        // Step column
        TableViewerColumn stepColumn = new TableViewerColumn(stepsTableViewer, SWT.NONE);
        stepColumn.getColumn().setText("Step");
        stepColumn.getColumn().setWidth(50);
        stepColumn.setLabelProvider(new ColumnLabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof ExecutionStep) {
                    return String.valueOf(((ExecutionStep) element).getStepNumber());
                }
                return "";
            }
        });
        
        // Status column
        TableViewerColumn statusColumn = new TableViewerColumn(stepsTableViewer, SWT.NONE);
        statusColumn.getColumn().setText("Status");
        statusColumn.getColumn().setWidth(100);
        statusColumn.setLabelProvider(new ColumnLabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof ExecutionStep) {
                    return ((ExecutionStep) element).getStatus().toString();
                }
                return "";
            }
        });
        
        // Action Type column
        TableViewerColumn actionColumn = new TableViewerColumn(stepsTableViewer, SWT.NONE);
        actionColumn.getColumn().setText("Action");
        actionColumn.getColumn().setWidth(100);
        actionColumn.setLabelProvider(new ColumnLabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof ExecutionStep) {
                    return ((ExecutionStep) element).getActionType();
                }
                return "";
            }
        });
        
        // Operation column
        TableViewerColumn operationColumn = new TableViewerColumn(stepsTableViewer, SWT.NONE);
        operationColumn.getColumn().setText("Operation");
        operationColumn.getColumn().setWidth(300);
        operationColumn.setLabelProvider(new ColumnLabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof ExecutionStep) {
                    String operation = ((ExecutionStep) element).getOperation();
                    return operation.length() > 50 ? operation.substring(0, 47) + "..." : operation;
                }
                return "";
            }
        });
        
        // Duration column
        TableViewerColumn durationColumn = new TableViewerColumn(stepsTableViewer, SWT.NONE);
        durationColumn.getColumn().setText("Duration");
        durationColumn.getColumn().setWidth(80);
        durationColumn.setLabelProvider(new ColumnLabelProvider() {
            @Override
            public String getText(Object element) {
                if (element instanceof ExecutionStep) {
                    long duration = ((ExecutionStep) element).getDurationMs();
                    return duration > 0 ? duration + "ms" : "";
                }
                return "";
            }
        });
    }
    
    private void createDetailsArea(Composite parent) {
        Composite detailsComposite = new Composite(parent, SWT.NONE);
        detailsComposite.setLayout(new GridLayout(1, false));
        
        Label detailsLabel = new Label(detailsComposite, SWT.NONE);
        detailsLabel.setText("Step Details:");
        detailsLabel.setLayoutData(new GridData(SWT.LEFT, SWT.TOP, false, false));
        
        detailsText = new Text(detailsComposite, SWT.BORDER | SWT.MULTI | SWT.V_SCROLL | SWT.H_SCROLL | SWT.READ_ONLY);
        detailsText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        detailsText.setText("Select a step to view details");
    }
    
    private void createActions() {
        IMenuManager menuManager = getViewSite().getActionBars().getMenuManager();
        IToolBarManager toolBarManager = getViewSite().getActionBars().getToolBarManager();
        
        // Refresh action
        Action refreshAction = new Action("Refresh") {
            @Override
            public void run() {
                refreshProgress();
            }
        };
        refreshAction.setToolTipText("Refresh the progress view");
        
        // Cancel action
        Action cancelAction = new Action("Cancel Task") {
            @Override
            public void run() {
                cancelCurrentTask();
            }
        };
        cancelAction.setToolTipText("Cancel the current task");
        
        // Add to menu and toolbar
        menuManager.add(refreshAction);
        menuManager.add(cancelAction);
        
        toolBarManager.add(refreshAction);
        toolBarManager.add(cancelAction);
    }
    
    private void startProgressMonitoring() {
        // Start a timer to periodically update the progress
        Display.getCurrent().timerExec(1000, new Runnable() {
            @Override
            public void run() {
                if (!progressBar.isDisposed()) {
                    refreshProgress();
                    // Schedule next update
                    Display.getCurrent().timerExec(1000, this);
                }
            }
        });
    }
    
    private void refreshProgress() {
        if (aiAgentCore == null) {
            return;
        }
        
        try {
            // Get current execution plan
            ExecutionPlan plan = null;
            if (aiAgentCore.getExecutionOrchestrator() != null) {
                plan = aiAgentCore.getExecutionOrchestrator().getCurrentPlan();
            }
            
            if (plan != currentPlan) {
                currentPlan = plan;
                updatePlanDisplay();
            }
            
            if (plan != null) {
                updateProgressDisplay(plan);
                updateStepsTable(plan);
            }
            
        } catch (Exception e) {
            logger.warn("Error refreshing progress", e);
        }
    }
    
    private void updatePlanDisplay() {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!goalLabel.isDisposed()) {
                    if (currentPlan != null) {
                        goalLabel.setText("Goal: " + currentPlan.getOriginalGoal());
                    } else {
                        goalLabel.setText("No active task");
                    }
                }
            }
        });
    }
    
    private void updateProgressDisplay(ExecutionPlan plan) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!progressBar.isDisposed() && !progressLabel.isDisposed()) {
                    int progress = plan.getProgressPercentage();
                    progressBar.setSelection(progress);
                    
                    String progressText = String.format("%d%% complete (%d/%d steps)", 
                        progress, plan.getCurrentStepIndex(), plan.getTotalSteps());
                    progressLabel.setText(progressText);
                }
            }
        });
    }
    
    private void updateStepsTable(ExecutionPlan plan) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                if (!stepsTableViewer.getControl().isDisposed()) {
                    stepsTableViewer.setInput(plan.getSteps());
                    stepsTableViewer.refresh();
                }
            }
        });
    }
    
    private void updateDetailsArea() {
        Object selection = stepsTableViewer.getStructuredSelection().getFirstElement();
        if (selection instanceof ExecutionStep) {
            ExecutionStep step = (ExecutionStep) selection;
            
            StringBuilder details = new StringBuilder();
            details.append("Step ").append(step.getStepNumber()).append("\n");
            details.append("Status: ").append(step.getStatus()).append("\n");
            details.append("Action Type: ").append(step.getActionType()).append("\n");
            details.append("Operation: ").append(step.getOperation()).append("\n");
            details.append("Expected Outcome: ").append(step.getExpectedOutcome()).append("\n");
            
            if (step.getDurationMs() > 0) {
                details.append("Duration: ").append(step.getDurationMs()).append("ms\n");
            }
            
            if (step.getResult() != null) {
                details.append("Result: ").append(step.getResult()).append("\n");
            }
            
            if (step.getErrorMessage() != null) {
                details.append("Error: ").append(step.getErrorMessage()).append("\n");
            }
            
            detailsText.setText(details.toString());
        } else {
            detailsText.setText("Select a step to view details");
        }
    }
    
    private void cancelCurrentTask() {
        if (aiAgentCore != null) {
            aiAgentCore.cancelCurrentTask();
        }
    }
    
    @Override
    public void setFocus() {
        if (stepsTableViewer != null && !stepsTableViewer.getControl().isDisposed()) {
            stepsTableViewer.getControl().setFocus();
        }
    }
}
