# Eclipse AI Agent Plugin

A comprehensive Eclipse plugin that integrates customizable AI agents for autonomous development assistance, compatible with Eclipse 2019-12 and Java 8.

## Features

### 🤖 AI Provider Integration
- Support for multiple AI providers (OpenAI, Anthropic, Azure OpenAI, Local LLMs)
- Secure API key storage using Eclipse's SecurePreferences
- Configurable endpoints and model parameters
- Graceful error handling and retry mechanisms

### 🎯 Autonomous Goal-Driven Assistance
- Natural language goal interpretation
- Intelligent task decomposition and planning
- Multi-step execution with self-correction
- Feedback loops for continuous improvement

### 🔧 Comprehensive IDE Integration
- **File System Operations**: Create, read, update, delete files and directories
- **Editor Integration**: Content manipulation, selection handling, syntax highlighting
- **Terminal Execution**: Command execution with output capture
- **Build Tool Support**: Maven, Gradle integration
- **Version Control**: Git operations and status monitoring

### 🛡️ Security & Safety
- User approval required for all destructive operations
- Diff previews for all proposed changes
- Secure encrypted storage for sensitive data
- Configurable data scrubbing for sensitive information

### 📊 User Interface
- **Chat View**: Natural language interaction with the AI agent
- **Progress Dashboard**: Real-time execution monitoring
- **Diff Approval Panel**: Review and approve proposed changes
- **Settings Page**: Comprehensive configuration options

## Installation

### Prerequisites
- Eclipse IDE 2019-12 (4.14) or compatible
- Java 8 or higher
- Internet connection for AI provider access

### Manual Installation
1. Download the plugin JAR file
2. Copy to Eclipse `dropins` folder
3. Restart Eclipse
4. Configure AI provider in Preferences > AI Agent

### From Source
1. Clone this repository
2. Import as Eclipse plugin project
3. Run as Eclipse Application for testing
4. Export as deployable plugin

## Configuration

### AI Provider Setup
1. Open **Window > Preferences > AI Agent**
2. Select your AI provider type
3. Configure the API endpoint
4. Enter your API key (stored securely)
5. Adjust model parameters as needed

### Security Settings
- **Require Approval**: Enable/disable user approval for changes
- **Data Scrubbing**: Configure sensitive data patterns
- **Logging**: Control what gets logged

### Agent Behavior
- **Autonomous Mode**: Enable self-directed task execution
- **Max Execution Steps**: Limit the number of steps per task
- **Self-Correction**: Enable automatic error recovery
- **Retry Attempts**: Configure retry behavior

## Usage

### Basic Chat Interaction
1. Open the AI Agent Chat view (**Window > Show View > Other > AI Agent > AI Agent Chat**)
2. Type your development goal or question
3. Press **Ctrl+Enter** or click **Send**
4. Monitor progress in the Progress view
5. Approve/reject proposed changes in the Diff Approval view

### Context Menu Actions
- Right-click on files/folders: **AI Agent > Send to AI Agent**
- Right-click on files/folders: **AI Agent > Analyze with AI**
- In code editor: **AI Agent > Explain Code**
- In code editor: **AI Agent > Refactor with AI**

### Keyboard Shortcuts
- **Ctrl+Alt+A**: Open AI Agent Chat
- **Ctrl+Alt+C**: Cancel current task

## Examples

### Code Generation
```
"Create a new Java class UserService with CRUD operations for a User entity"
```

### Bug Fixing
```
"Fix the NullPointerException in UserController.java line 45"
```

### Refactoring
```
"Refactor the calculateTotal method to use streams and make it more readable"
```

### Testing
```
"Generate unit tests for the UserService class with 80% coverage"
```

### Documentation
```
"Add JavaDoc comments to all public methods in the UserRepository class"
```

## Architecture

### Core Components
- **AIAgentCore**: Central orchestrator
- **ContextManager**: Environment state management
- **PlanningEngine**: Goal decomposition and planning
- **ExecutionOrchestrator**: Step-by-step execution
- **FeedbackLoop**: Self-correction and learning

### IDE Integration Adapters
- **FileSystemAdapter**: File and directory operations
- **EditorAdapter**: Text editor integration
- **TerminalAdapter**: Command execution
- **BuildToolAdapter**: Maven/Gradle integration

### AI Provider Layer
- **AIProviderClient**: HTTP communication
- **Provider Handlers**: OpenAI, Anthropic, Azure, Local LLM support
- **SecureStorage**: Encrypted API key management

## Development

### Building from Source
```bash
# Clone the repository
git clone https://github.com/fartech/eclipse-ai-agent.git
cd eclipse-ai-agent

# Import into Eclipse as plugin project
# Run as Eclipse Application for testing
```

### Dependencies
- Eclipse Platform APIs
- Apache HttpClient 4.5.14
- Gson 2.10.1
- JGit (for Git integration)
- Maven/Gradle APIs

### Testing
```bash
# Run unit tests
mvn test

# Run integration tests
mvn integration-test
```

## Troubleshooting

### Common Issues

**Plugin doesn't start**
- Check Eclipse error log
- Verify Java 8 compatibility
- Ensure all dependencies are available

**AI requests fail**
- Verify API key configuration
- Check network connectivity
- Review provider-specific error messages

**Performance issues**
- Reduce max tokens in preferences
- Disable detailed logging
- Check available memory

### Logging
- Console output: **Window > Show View > Console > AI Agent**
- Eclipse error log: **Window > Show View > Error Log**
- Plugin logs: Workspace `.metadata/.log`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

- GitHub Issues: Report bugs and feature requests
- Documentation: See the `docs/` directory
- Examples: Check the `examples/` directory

## Roadmap

### Version 1.1
- [ ] Enhanced Anthropic Claude support
- [ ] Local LLM integration (Ollama)
- [ ] Advanced diff visualization
- [ ] Plugin marketplace integration

### Version 1.2
- [ ] Multi-language support
- [ ] Advanced debugging integration
- [ ] Team collaboration features
- [ ] Custom AI model training

## Acknowledgments

- Eclipse Platform team for the excellent plugin framework
- OpenAI and Anthropic for AI model access
- The open-source community for inspiration and feedback
