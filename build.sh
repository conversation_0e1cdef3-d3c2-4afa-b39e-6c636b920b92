#!/bin/bash
# Build script for Eclipse AI Agent Plugin
# Compatible with Linux/macOS environments

echo "========================================"
echo "Building Eclipse AI Agent Plugin"
echo "========================================"

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "ERROR: <PERSON>ven is not installed or not in PATH"
    echo "Please install <PERSON>ven and add it to your PATH"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install Java 8 or higher and add it to your PATH"
    exit 1
fi

# Display Java version
echo "Checking Java version..."
java -version

# Clean previous build
echo ""
echo "Cleaning previous build..."
mvn clean

if [ $? -ne 0 ]; then
    echo "ERROR: Clean failed"
    exit 1
fi

# Copy dependencies
echo ""
echo "Copying dependencies..."
mvn dependency:copy-dependencies

if [ $? -ne 0 ]; then
    echo "ERROR: Dependency copy failed"
    exit 1
fi

# Run tests
echo ""
echo "Running tests..."
mvn test

if [ $? -ne 0 ]; then
    echo "WARNING: Some tests failed"
    echo "Continuing with build..."
fi

# Compile and package
echo ""
echo "Compiling and packaging..."
mvn compile package

if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo ""
echo "========================================"
echo "Build completed successfully!"
echo "========================================"
echo ""
echo "Plugin JAR location: target/aiagent-1.0.0-SNAPSHOT.jar"
echo "Dependencies location: lib/"
echo ""
echo "To install in Eclipse:"
echo "1. Copy the JAR file to Eclipse dropins folder"
echo "2. Copy the lib folder contents to Eclipse dropins folder"
echo "3. Restart Eclipse"
echo ""
echo "Or use Eclipse's 'Install New Software' feature"
echo "with the generated update site."
echo "========================================"
