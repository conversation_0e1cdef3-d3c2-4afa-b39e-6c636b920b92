package com.fartech.eclipse.aiagent.ui.actions;

import org.eclipse.core.resources.IResource;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.ui.IObjectActionDelegate;
import org.eclipse.ui.IWorkbenchPart;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Action for analyzing selected resources with AI.
 */
public class AnalyzeWithAIAction implements IObjectActionDelegate {
    
    private AIAgentLogger logger;
    private IWorkbenchPart targetPart;
    private IResource selectedResource;
    
    public AnalyzeWithAIAction() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void setActivePart(IAction action, IWorkbenchPart targetPart) {
        this.targetPart = targetPart;
    }
    
    @Override
    public void selectionChanged(IAction action, ISelection selection) {
        selectedResource = null;
        
        if (selection instanceof IStructuredSelection) {
            IStructuredSelection structuredSelection = (IStructuredSelection) selection;
            Object firstElement = structuredSelection.getFirstElement();
            
            if (firstElement instanceof IResource) {
                selectedResource = (IResource) firstElement;
                action.setEnabled(true);
            } else {
                action.setEnabled(false);
            }
        } else {
            action.setEnabled(false);
        }
    }
    
    @Override
    public void run(IAction action) {
        if (selectedResource == null) {
            logger.warn("No resource selected for AI analysis");
            return;
        }
        
        try {
            // TODO: Implement direct analysis without opening chat
            logger.info("Analyzing resource with AI: " + selectedResource.getFullPath());
            
            // For now, just log the action
            logger.info("AI analysis not yet implemented");
            
        } catch (Exception e) {
            logger.error("Error analyzing resource with AI", e);
        }
    }
}
