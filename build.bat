@echo off
REM Build script for Eclipse AI Agent Plugin
REM Compatible with Windows environments

echo ========================================
echo Building Eclipse AI Agent Plugin
echo ========================================

REM Check if <PERSON><PERSON> is available
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: <PERSON><PERSON> is not installed or not in PATH
    echo Please install Maven and add it to your PATH
    exit /b 1
)

REM Check if Java is available
where java >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher and add it to your PATH
    exit /b 1
)

REM Display Java version
echo Checking Java version...
java -version

REM Clean previous build
echo.
echo Cleaning previous build...
mvn clean

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Clean failed
    exit /b 1
)

REM Copy dependencies
echo.
echo Copying dependencies...
mvn dependency:copy-dependencies

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Dependency copy failed
    exit /b 1
)

REM Run tests
echo.
echo Running tests...
mvn test

if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Some tests failed
    echo Continuing with build...
)

REM Compile and package
echo.
echo Compiling and packaging...
mvn compile package

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Plugin JAR location: target\aiagent-1.0.0-SNAPSHOT.jar
echo Dependencies location: lib\
echo.
echo To install in Eclipse:
echo 1. Copy the JAR file to Eclipse dropins folder
echo 2. Copy the lib folder contents to Eclipse dropins folder
echo 3. Restart Eclipse
echo.
echo Or use Eclipse's "Install New Software" feature
echo with the generated update site.
echo ========================================

pause
