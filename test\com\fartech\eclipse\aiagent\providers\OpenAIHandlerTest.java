package com.fartech.eclipse.aiagent.providers;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpPost;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;
import com.google.gson.Gson;

/**
 * Unit tests for OpenAIHandler.
 */
public class OpenAIHandlerTest {
    
    @Mock
    private AIAgentLogger mockLogger;
    
    @Mock
    private HttpResponse mockHttpResponse;
    
    @Mock
    private StatusLine mockStatusLine;
    
    @Mock
    private HttpEntity mockHttpEntity;
    
    private Gson gson;
    private OpenAIHandler openAIHandler;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        gson = new Gson();
        openAIHandler = new OpenAIHandler(gson, mockLogger);
    }
    
    @Test
    public void testGetProviderType() {
        assertEquals("openai", openAIHandler.getProviderType());
    }
    
    @Test
    public void testGetDefaultModel() {
        assertEquals("gpt-4", openAIHandler.getDefaultModel());
    }
    
    @Test
    public void testGetMaxTokens() {
        assertEquals(8192, openAIHandler.getMaxTokens());
    }
    
    @Test
    public void testValidateApiKey() {
        // Valid API key
        assertTrue(openAIHandler.validateApiKey("sk-1234567890abcdef1234567890abcdef1234567890abcdef"));
        
        // Invalid API keys
        assertFalse(openAIHandler.validateApiKey(null));
        assertFalse(openAIHandler.validateApiKey(""));
        assertFalse(openAIHandler.validateApiKey("invalid-key"));
        assertFalse(openAIHandler.validateApiKey("sk-short"));
        assertFalse(openAIHandler.validateApiKey("test-key"));
    }
    
    @Test
    public void testBuildHttpRequest() throws Exception {
        String endpoint = "https://api.openai.com/v1/chat/completions";
        String apiKey = "sk-1234567890abcdef1234567890abcdef1234567890abcdef";
        AIRequest request = new AIRequest("Test prompt", 100, 0.7);
        request.setSystemMessage("You are a helpful assistant");
        
        HttpPost httpPost = openAIHandler.buildHttpRequest(endpoint, apiKey, request);
        
        assertNotNull(httpPost);
        assertEquals(endpoint, httpPost.getURI().toString());
        assertEquals("application/json", httpPost.getFirstHeader("Content-Type").getValue());
        assertEquals("Bearer " + apiKey, httpPost.getFirstHeader("Authorization").getValue());
        assertNotNull(httpPost.getEntity());
    }
    
    @Test
    public void testBuildHttpRequestWithContext() throws Exception {
        String endpoint = "https://api.openai.com/v1/chat/completions";
        String apiKey = "sk-1234567890abcdef1234567890abcdef1234567890abcdef";
        AIRequest request = new AIRequest("Test prompt", 100, 0.7);
        request.addContext("Previous conversation context");
        request.addContext("Additional context");
        
        HttpPost httpPost = openAIHandler.buildHttpRequest(endpoint, apiKey, request);
        
        assertNotNull(httpPost);
        assertNotNull(httpPost.getEntity());
    }
    
    @Test
    public void testProcessSuccessfulResponse() throws Exception {
        // Mock successful response
        when(mockHttpResponse.getStatusLine()).thenReturn(mockStatusLine);
        when(mockStatusLine.getStatusCode()).thenReturn(200);
        when(mockHttpResponse.getEntity()).thenReturn(mockHttpEntity);
        
        String responseJson = "{\n" +
            "  \"id\": \"chatcmpl-123\",\n" +
            "  \"object\": \"chat.completion\",\n" +
            "  \"created\": **********,\n" +
            "  \"model\": \"gpt-4\",\n" +
            "  \"choices\": [{\n" +
            "    \"index\": 0,\n" +
            "    \"message\": {\n" +
            "      \"role\": \"assistant\",\n" +
            "      \"content\": \"Hello! How can I help you today?\"\n" +
            "    },\n" +
            "    \"finish_reason\": \"stop\"\n" +
            "  }],\n" +
            "  \"usage\": {\n" +
            "    \"prompt_tokens\": 9,\n" +
            "    \"completion_tokens\": 12,\n" +
            "    \"total_tokens\": 21\n" +
            "  }\n" +
            "}";
        
        // Mock entity content
        when(mockHttpEntity.getContent()).thenReturn(
            new java.io.ByteArrayInputStream(responseJson.getBytes())
        );
        
        AIResponse response = openAIHandler.processResponse(mockHttpResponse);
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("Hello! How can I help you today?", response.getContent());
        assertEquals("gpt-4", response.getModel());
        assertEquals(21, response.getTokensUsed());
        assertEquals("stop", response.getFinishReason());
    }
    
    @Test
    public void testProcessErrorResponse() throws Exception {
        // Mock error response
        when(mockHttpResponse.getStatusLine()).thenReturn(mockStatusLine);
        when(mockStatusLine.getStatusCode()).thenReturn(400);
        when(mockHttpResponse.getEntity()).thenReturn(mockHttpEntity);
        
        String errorJson = "{\n" +
            "  \"error\": {\n" +
            "    \"message\": \"Invalid request\",\n" +
            "    \"type\": \"invalid_request_error\",\n" +
            "    \"code\": \"invalid_api_key\"\n" +
            "  }\n" +
            "}";
        
        // Mock entity content
        when(mockHttpEntity.getContent()).thenReturn(
            new java.io.ByteArrayInputStream(errorJson.getBytes())
        );
        
        try {
            openAIHandler.processResponse(mockHttpResponse);
            fail("Expected AIProviderException was not thrown");
        } catch (AIProviderException e) {
            assertEquals("openai", e.getProviderType());
            assertEquals(400, e.getHttpStatusCode());
            assertEquals("invalid_api_key", e.getProviderErrorCode());
            assertTrue(e.getMessage().contains("Invalid request"));
        }
    }
    
    @Test
    public void testProcessResponseWithMissingContent() throws Exception {
        // Mock response with no content
        when(mockHttpResponse.getStatusLine()).thenReturn(mockStatusLine);
        when(mockStatusLine.getStatusCode()).thenReturn(200);
        when(mockHttpResponse.getEntity()).thenReturn(mockHttpEntity);
        
        String responseJson = "{\n" +
            "  \"id\": \"chatcmpl-123\",\n" +
            "  \"object\": \"chat.completion\",\n" +
            "  \"created\": **********,\n" +
            "  \"model\": \"gpt-4\",\n" +
            "  \"choices\": []\n" +
            "}";
        
        // Mock entity content
        when(mockHttpEntity.getContent()).thenReturn(
            new java.io.ByteArrayInputStream(responseJson.getBytes())
        );
        
        try {
            openAIHandler.processResponse(mockHttpResponse);
            fail("Expected AIProviderException was not thrown");
        } catch (AIProviderException e) {
            assertTrue(e.getMessage().contains("No content in OpenAI response"));
        }
    }
    
    @Test
    public void testBuildHttpRequestWithNullParameters() {
        try {
            openAIHandler.buildHttpRequest(null, "api-key", new AIRequest("test"));
            fail("Expected AIProviderException was not thrown");
        } catch (AIProviderException e) {
            // Expected
        }
        
        try {
            openAIHandler.buildHttpRequest("endpoint", null, new AIRequest("test"));
            fail("Expected AIProviderException was not thrown");
        } catch (AIProviderException e) {
            // Expected
        }
        
        try {
            openAIHandler.buildHttpRequest("endpoint", "api-key", null);
            fail("Expected AIProviderException was not thrown");
        } catch (AIProviderException e) {
            // Expected
        }
    }
}
