package com.fartech.eclipse.aiagent.providers.models;

/**
 * Represents a response from an AI provider.
 */
public class AIResponse {
    
    private String content;
    private boolean success;
    private String errorMessage;
    private int tokensUsed;
    private long responseTimeMs;
    private String model;
    private String finishReason;
    
    public AIResponse() {
        this.success = false;
        this.tokensUsed = 0;
        this.responseTimeMs = 0;
    }
    
    public AIResponse(String content) {
        this();
        this.content = content;
        this.success = true;
    }
    
    public AIResponse(String errorMessage, boolean isError) {
        this();
        if (isError) {
            this.errorMessage = errorMessage;
            this.success = false;
        } else {
            this.content = errorMessage;
            this.success = true;
        }
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.success = false;
    }
    
    public int getTokensUsed() {
        return tokensUsed;
    }
    
    public void setTokensUsed(int tokensUsed) {
        this.tokensUsed = tokensUsed;
    }
    
    public long getResponseTimeMs() {
        return responseTimeMs;
    }
    
    public void setResponseTimeMs(long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getFinishReason() {
        return finishReason;
    }
    
    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }
    
    /**
     * Check if the response has content
     */
    public boolean hasContent() {
        return content != null && !content.trim().isEmpty();
    }
    
    /**
     * Get a safe content string (never null)
     */
    public String getSafeContent() {
        return content != null ? content : "";
    }
    
    /**
     * Create a successful response
     */
    public static AIResponse success(String content) {
        AIResponse response = new AIResponse(content);
        response.setSuccess(true);
        return response;
    }
    
    /**
     * Create an error response
     */
    public static AIResponse error(String errorMessage) {
        AIResponse response = new AIResponse();
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
    
    /**
     * Create an error response with exception
     */
    public static AIResponse error(String errorMessage, Throwable throwable) {
        AIResponse response = new AIResponse();
        String fullMessage = errorMessage;
        if (throwable != null) {
            fullMessage += ": " + throwable.getMessage();
        }
        response.setErrorMessage(fullMessage);
        response.setSuccess(false);
        return response;
    }
    
    @Override
    public String toString() {
        return "AIResponse{" +
                "success=" + success +
                ", content='" + (content != null ? content.substring(0, Math.min(100, content.length())) + "..." : "null") + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", tokensUsed=" + tokensUsed +
                ", responseTimeMs=" + responseTimeMs +
                ", model='" + model + '\'' +
                ", finishReason='" + finishReason + '\'' +
                '}';
    }
}
