package com.fartech.eclipse.aiagent.providers;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;

import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;

/**
 * Interface for AI provider-specific handlers.
 * Each AI provider (OpenAI, Anthropic, etc.) implements this interface
 * to handle provider-specific request/response formatting.
 */
public interface AIProviderHandler {
    
    /**
     * Build an HTTP request for the specific AI provider
     * @param endpoint the API endpoint
     * @param apiKey the API key
     * @param request the AI request
     * @return the configured HTTP POST request
     * @throws AIProviderException if request building fails
     */
    HttpPost buildHttpRequest(String endpoint, String apiKey, AIRequest request) throws AIProviderException;
    
    /**
     * Process the HTTP response from the AI provider
     * @param response the HTTP response
     * @return the processed AI response
     * @throws AIProviderException if response processing fails
     */
    AIResponse processResponse(HttpResponse response) throws AIProviderException;
    
    /**
     * Get the provider type identifier
     * @return the provider type (e.g., "openai", "anthropic")
     */
    String getProviderType();
    
    /**
     * Validate the API key format for this provider
     * @param apiKey the API key to validate
     * @return true if the format is valid, false otherwise
     */
    boolean validateApiKey(String apiKey);
    
    /**
     * Get the default model for this provider
     * @return the default model name
     */
    String getDefaultModel();
    
    /**
     * Get the maximum tokens supported by this provider
     * @return the maximum token limit
     */
    int getMaxTokens();
}
