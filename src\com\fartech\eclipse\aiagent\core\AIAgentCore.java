package com.fartech.eclipse.aiagent.core;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.context.ContextManager;
import com.fartech.eclipse.aiagent.core.execution.ExecutionOrchestrator;
import com.fartech.eclipse.aiagent.core.feedback.FeedbackLoop;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.planning.PlanningEngine;
import com.fartech.eclipse.aiagent.core.security.SecureStorage;
import com.fartech.eclipse.aiagent.providers.AIProviderClient;

/**
 * Core AI Agent class that orchestrates all the main components.
 * This is the central brain of the AI Agent plugin.
 */
public class AIAgentCore {
    
    private final AIAgentLogger logger;
    private final SecureStorage secureStorage;
    private final ContextManager contextManager;
    private final PlanningEngine planningEngine;
    private final ExecutionOrchestrator executionOrchestrator;
    private final FeedbackLoop feedbackLoop;
    private final AIProviderClient aiProviderClient;
    
    private final ExecutorService executorService;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    
    private volatile Future<?> currentTask;
    
    public AIAgentCore() {
        this.logger = Activator.getDefault().getLogger();
        this.secureStorage = new SecureStorage();
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "AI-Agent-Worker");
            t.setDaemon(true);
            return t;
        });
        
        // Initialize core components
        this.contextManager = new ContextManager();
        this.aiProviderClient = new AIProviderClient(secureStorage);
        this.planningEngine = new PlanningEngine(contextManager, aiProviderClient);
        this.executionOrchestrator = new ExecutionOrchestrator(contextManager);
        this.feedbackLoop = new FeedbackLoop(contextManager, planningEngine, aiProviderClient);
    }
    
    /**
     * Initialize the AI Agent Core and all its components
     */
    public void initialize() throws Exception {
        if (initialized.get()) {
            logger.warn("AI Agent Core already initialized");
            return;
        }
        
        try {
            logger.info("Initializing AI Agent Core...");
            
            // Initialize components in dependency order
            contextManager.initialize();
            aiProviderClient.initialize();
            planningEngine.initialize();
            executionOrchestrator.initialize();
            feedbackLoop.initialize();
            
            // Wire up component relationships
            executionOrchestrator.setFeedbackLoop(feedbackLoop);
            feedbackLoop.setExecutionOrchestrator(executionOrchestrator);
            
            initialized.set(true);
            logger.info("AI Agent Core initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize AI Agent Core", e);
            throw e;
        }
    }
    
    /**
     * Process a user request asynchronously
     * @param userInput the user's natural language input
     * @return a Future representing the task execution
     */
    public Future<?> processUserRequest(String userInput) {
        if (!initialized.get()) {
            throw new IllegalStateException("AI Agent Core not initialized");
        }
        
        if (shutdown.get()) {
            throw new IllegalStateException("AI Agent Core is shut down");
        }
        
        // Cancel any existing task
        cancelCurrentTask();
        
        logger.info("Processing user request: " + userInput);
        
        currentTask = executorService.submit(() -> {
            try {
                // Update context with user input
                contextManager.setUserInput(userInput);
                
                // Generate execution plan
                logger.debug("Generating execution plan...");
                planningEngine.generatePlan(userInput);
                
                // Execute the plan
                logger.debug("Executing plan...");
                executionOrchestrator.executePlan(planningEngine.getCurrentPlan());
                
                logger.info("User request processed successfully");
                
            } catch (InterruptedException e) {
                logger.info("Task was cancelled");
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                logger.error("Error processing user request", e);
                // Notify feedback loop of the error
                feedbackLoop.handleExecutionError(e);
            }
        });
        
        return currentTask;
    }
    
    /**
     * Cancel the currently running task
     */
    public void cancelCurrentTask() {
        if (currentTask != null && !currentTask.isDone()) {
            logger.info("Cancelling current task");
            currentTask.cancel(true);
            
            // Also cancel any ongoing operations in components
            executionOrchestrator.cancelCurrentExecution();
            planningEngine.cancelCurrentPlanning();
        }
    }
    
    /**
     * Check if a task is currently running
     */
    public boolean isTaskRunning() {
        return currentTask != null && !currentTask.isDone();
    }
    
    /**
     * Get the current execution status
     */
    public String getCurrentStatus() {
        if (!initialized.get()) {
            return "Not initialized";
        }
        
        if (shutdown.get()) {
            return "Shut down";
        }
        
        if (isTaskRunning()) {
            return executionOrchestrator.getCurrentStatus();
        }
        
        return "Idle";
    }
    
    /**
     * Shutdown the AI Agent Core and cleanup resources
     */
    public void shutdown() {
        if (shutdown.getAndSet(true)) {
            return; // Already shut down
        }
        
        logger.info("Shutting down AI Agent Core...");
        
        try {
            // Cancel any running tasks
            cancelCurrentTask();
            
            // Shutdown components
            if (feedbackLoop != null) {
                feedbackLoop.shutdown();
            }
            
            if (executionOrchestrator != null) {
                executionOrchestrator.shutdown();
            }
            
            if (planningEngine != null) {
                planningEngine.shutdown();
            }
            
            if (aiProviderClient != null) {
                aiProviderClient.shutdown();
            }
            
            if (contextManager != null) {
                contextManager.shutdown();
            }
            
            // Shutdown executor service
            executorService.shutdown();
            
            logger.info("AI Agent Core shut down successfully");
            
        } catch (Exception e) {
            logger.error("Error during AI Agent Core shutdown", e);
        }
    }
    
    // Getters for components (for UI and other integrations)
    
    public ContextManager getContextManager() {
        return contextManager;
    }
    
    public PlanningEngine getPlanningEngine() {
        return planningEngine;
    }
    
    public ExecutionOrchestrator getExecutionOrchestrator() {
        return executionOrchestrator;
    }
    
    public FeedbackLoop getFeedbackLoop() {
        return feedbackLoop;
    }
    
    public AIProviderClient getAIProviderClient() {
        return aiProviderClient;
    }
    
    public SecureStorage getSecureStorage() {
        return secureStorage;
    }
}
