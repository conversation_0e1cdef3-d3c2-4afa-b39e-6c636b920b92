package com.fartech.eclipse.aiagent.core.security;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.eclipse.equinox.security.storage.ISecurePreferences;
import org.eclipse.equinox.security.storage.StorageException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Unit tests for SecureStorage.
 * Note: These tests use mocks since Eclipse SecurePreferences requires a running Eclipse platform.
 */
public class SecureStorageTest {
    
    @Mock
    private ISecurePreferences mockSecurePrefs;
    
    private SecureStorage secureStorage;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Note: In a real test environment, we would need to mock the SecurePreferencesFactory
        // For now, we'll test the validation methods and logic
    }
    
    @Test
    public void testIsValidApiKey() {
        // Valid API keys
        assertTrue(SecureStorage.isValidApiKey("sk-1234567890abcdef1234567890abcdef1234567890abcdef"));
        assertTrue(SecureStorage.isValidApiKey("valid-api-key-12345"));
        assertTrue(SecureStorage.isValidApiKey("a".repeat(20))); // Minimum length
        
        // Invalid API keys
        assertFalse(SecureStorage.isValidApiKey(null));
        assertFalse(SecureStorage.isValidApiKey(""));
        assertFalse(SecureStorage.isValidApiKey("   "));
        assertFalse(SecureStorage.isValidApiKey("short"));
        assertFalse(SecureStorage.isValidApiKey("test"));
        assertFalse(SecureStorage.isValidApiKey("example"));
        assertFalse(SecureStorage.isValidApiKey("placeholder"));
        assertFalse(SecureStorage.isValidApiKey("your-api-key"));
        assertFalse(SecureStorage.isValidApiKey("TEST-KEY"));
    }
    
    @Test
    public void testStoreApiKeyValidation() {
        // Test with null provider type
        try {
            secureStorage = new SecureStorage();
            secureStorage.storeApiKey(null, "valid-key");
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
        
        // Test with empty provider type
        try {
            secureStorage.storeApiKey("", "valid-key");
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
        
        // Test with null API key
        try {
            secureStorage.storeApiKey("openai", null);
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("API key cannot be null", e.getMessage());
        }
    }
    
    @Test
    public void testGetApiKeyValidation() {
        secureStorage = new SecureStorage();
        
        // Test with null provider type
        try {
            secureStorage.getApiKey(null);
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
        
        // Test with empty provider type
        try {
            secureStorage.getApiKey("");
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
    }
    
    @Test
    public void testRemoveApiKeyValidation() {
        secureStorage = new SecureStorage();
        
        // Test with null provider type
        try {
            secureStorage.removeApiKey(null);
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
        
        // Test with empty provider type
        try {
            secureStorage.removeApiKey("");
            fail("Expected IllegalArgumentException was not thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("Provider type cannot be null or empty", e.getMessage());
        }
    }
    
    @Test
    public void testHasApiKeyValidation() {
        secureStorage = new SecureStorage();
        
        // Test with null provider type
        assertFalse(secureStorage.hasApiKey(null));
        
        // Test with empty provider type
        assertFalse(secureStorage.hasApiKey(""));
        
        // Test with whitespace provider type
        assertFalse(secureStorage.hasApiKey("   "));
    }
    
    // Mock-based tests for the actual storage operations
    // Note: These would require proper Eclipse platform setup in a real test environment
    
    @Test
    public void testMockStoreApiKey() throws Exception {
        // This test demonstrates how the storage operations would work with proper mocking
        String providerType = "openai";
        String apiKey = "sk-test1234567890abcdef";
        String expectedKey = "api.key.openai";
        
        // Mock the secure preferences behavior
        doNothing().when(mockSecurePrefs).put(expectedKey, apiKey, true);
        doNothing().when(mockSecurePrefs).flush();
        
        // In a real implementation, we would inject the mock or use a factory
        // For now, this demonstrates the expected behavior
        
        verify(mockSecurePrefs, never()).put(anyString(), anyString(), anyBoolean());
        verify(mockSecurePrefs, never()).flush();
    }
    
    @Test
    public void testMockGetApiKey() throws Exception {
        String providerType = "openai";
        String expectedKey = "api.key.openai";
        String expectedApiKey = "sk-test1234567890abcdef";
        
        // Mock the secure preferences behavior
        when(mockSecurePrefs.get(expectedKey, null)).thenReturn(expectedApiKey);
        
        // In a real implementation, we would inject the mock
        // String result = secureStorage.getApiKey(providerType);
        // assertEquals(expectedApiKey, result);
        
        verify(mockSecurePrefs, never()).get(anyString(), any());
    }
    
    @Test
    public void testMockStorageException() throws Exception {
        String providerType = "openai";
        String apiKey = "sk-test1234567890abcdef";
        String expectedKey = "api.key.openai";
        
        // Mock storage exception
        doThrow(new StorageException(StorageException.NO_SECURE_STORAGE, "No secure storage"))
            .when(mockSecurePrefs).put(expectedKey, apiKey, true);
        
        // In a real implementation, this would test exception handling
        // try {
        //     secureStorage.storeApiKey(providerType, apiKey);
        //     fail("Expected SecurityException was not thrown");
        // } catch (SecurityException e) {
        //     assertTrue(e.getMessage().contains("Failed to store API key securely"));
        // }
    }
    
    @Test
    public void testApiKeyKeyGeneration() {
        // Test that provider types are properly converted to storage keys
        String[] providerTypes = {"openai", "OpenAI", "OPENAI", "anthropic", "Anthropic"};
        String[] expectedKeys = {"api.key.openai", "api.key.openai", "api.key.openai", 
                                "api.key.anthropic", "api.key.anthropic"};
        
        // This would be tested in the actual implementation
        for (int i = 0; i < providerTypes.length; i++) {
            String providerType = providerTypes[i];
            String expectedKey = expectedKeys[i];
            
            // The actual key generation logic would be:
            String actualKey = "api.key." + providerType.toLowerCase();
            assertEquals(expectedKey, actualKey);
        }
    }
}
