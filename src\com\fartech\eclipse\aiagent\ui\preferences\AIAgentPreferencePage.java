package com.fartech.eclipse.aiagent.ui.preferences;

import org.eclipse.jface.preference.BooleanFieldEditor;
import org.eclipse.jface.preference.ComboFieldEditor;
import org.eclipse.jface.preference.FieldEditorPreferencePage;
import org.eclipse.jface.preference.IntegerFieldEditor;
import org.eclipse.jface.preference.StringFieldEditor;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.MessageBox;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.IWorkbench;
import org.eclipse.ui.IWorkbenchPreferencePage;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.core.security.SecureStorage;

/**
 * Preference page for configuring the AI Agent plugin.
 */
public class AIAgentPreferencePage extends FieldEditorPreferencePage implements IWorkbenchPreferencePage {
    
    private Text apiKeyText;
    private Button testConnectionButton;
    private Label connectionStatusLabel;
    
    public AIAgentPreferencePage() {
        super(GRID);
        setPreferenceStore(Activator.getDefault().getPreferenceStore());
        setDescription("Configure the AI Agent plugin settings");
    }
    
    @Override
    public void createFieldEditors() {
        Composite parent = getFieldEditorParent();
        
        // AI Provider Configuration Group
        createAIProviderGroup(parent);
        
        // Security Settings Group
        createSecurityGroup(parent);
        
        // Agent Behavior Group
        createAgentBehaviorGroup(parent);
        
        // UI Settings Group
        createUISettingsGroup(parent);
        
        // Logging Group
        createLoggingGroup(parent);
    }
    
    private void createAIProviderGroup(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("AI Provider Configuration");
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        group.setLayout(new GridLayout(1, false));
        
        Composite groupComposite = new Composite(group, SWT.NONE);
        groupComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        // Provider Type
        String[][] providerTypes = {
            {"OpenAI", "openai"},
            {"Anthropic Claude", "anthropic"},
            {"Azure OpenAI", "azure"},
            {"Local LLM", "local"},
            {"Custom", "custom"}
        };
        addField(new ComboFieldEditor(AIAgentPreferences.PREF_AI_PROVIDER_TYPE, 
            "Provider Type:", providerTypes, groupComposite));
        
        // API Endpoint
        addField(new StringFieldEditor(AIAgentPreferences.PREF_AI_PROVIDER_ENDPOINT, 
            "API Endpoint:", groupComposite));
        
        // Model
        addField(new StringFieldEditor(AIAgentPreferences.PREF_AI_PROVIDER_MODEL, 
            "Model:", groupComposite));
        
        // API Key section
        createApiKeySection(groupComposite);
        
        // Timeout
        IntegerFieldEditor timeoutEditor = new IntegerFieldEditor(AIAgentPreferences.PREF_AI_PROVIDER_TIMEOUT, 
            "Timeout (ms):", groupComposite);
        timeoutEditor.setValidRange(1000, 300000); // 1 second to 5 minutes
        addField(timeoutEditor);
        
        // Max Tokens
        IntegerFieldEditor maxTokensEditor = new IntegerFieldEditor(AIAgentPreferences.PREF_AI_PROVIDER_MAX_TOKENS, 
            "Max Tokens:", groupComposite);
        maxTokensEditor.setValidRange(100, 32000);
        addField(maxTokensEditor);
    }
    
    private void createApiKeySection(Composite parent) {
        Composite apiKeyComposite = new Composite(parent, SWT.NONE);
        apiKeyComposite.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        GridLayout layout = new GridLayout(3, false);
        layout.marginWidth = 0;
        apiKeyComposite.setLayout(layout);
        
        Label apiKeyLabel = new Label(apiKeyComposite, SWT.NONE);
        apiKeyLabel.setText("API Key:");
        apiKeyLabel.setLayoutData(new GridData(SWT.LEFT, SWT.CENTER, false, false));
        
        apiKeyText = new Text(apiKeyComposite, SWT.BORDER | SWT.PASSWORD);
        apiKeyText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        
        testConnectionButton = new Button(apiKeyComposite, SWT.PUSH);
        testConnectionButton.setText("Test Connection");
        testConnectionButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                testConnection();
            }
        });
        
        connectionStatusLabel = new Label(apiKeyComposite, SWT.NONE);
        connectionStatusLabel.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false, 3, 1));
        
        // Load existing API key
        loadApiKey();
    }
    
    private void createSecurityGroup(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("Security Settings");
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        group.setLayout(new GridLayout(1, false));
        
        Composite groupComposite = new Composite(group, SWT.NONE);
        groupComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_REQUIRE_APPROVAL, 
            "Require approval for all changes", groupComposite));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_ENABLE_DATA_SCRUBBING, 
            "Enable data scrubbing for sensitive information", groupComposite));
        
        addField(new StringFieldEditor(AIAgentPreferences.PREF_SENSITIVE_PATTERNS, 
            "Sensitive patterns (comma-separated):", groupComposite));
    }
    
    private void createAgentBehaviorGroup(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("Agent Behavior");
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        group.setLayout(new GridLayout(1, false));
        
        Composite groupComposite = new Composite(group, SWT.NONE);
        groupComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_ENABLE_AUTONOMOUS_MODE, 
            "Enable autonomous mode", groupComposite));
        
        IntegerFieldEditor maxStepsEditor = new IntegerFieldEditor(AIAgentPreferences.PREF_MAX_EXECUTION_STEPS, 
            "Max execution steps:", groupComposite);
        maxStepsEditor.setValidRange(1, 50);
        addField(maxStepsEditor);
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_ENABLE_SELF_CORRECTION, 
            "Enable self-correction", groupComposite));
        
        IntegerFieldEditor maxRetriesEditor = new IntegerFieldEditor(AIAgentPreferences.PREF_MAX_RETRY_ATTEMPTS, 
            "Max retry attempts:", groupComposite);
        maxRetriesEditor.setValidRange(1, 10);
        addField(maxRetriesEditor);
    }
    
    private void createUISettingsGroup(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("UI Settings");
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        group.setLayout(new GridLayout(1, false));
        
        Composite groupComposite = new Composite(group, SWT.NONE);
        groupComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_SHOW_PROGRESS_DETAILS, 
            "Show detailed progress information", groupComposite));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_AUTO_OPEN_DIFF_VIEW, 
            "Auto-open diff view for changes", groupComposite));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_ENABLE_SYNTAX_HIGHLIGHTING, 
            "Enable syntax highlighting in chat", groupComposite));
    }
    
    private void createLoggingGroup(Composite parent) {
        Group group = new Group(parent, SWT.NONE);
        group.setText("Logging");
        group.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        group.setLayout(new GridLayout(1, false));
        
        Composite groupComposite = new Composite(group, SWT.NONE);
        groupComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        
        String[][] logLevels = {
            {"DEBUG", "DEBUG"},
            {"INFO", "INFO"},
            {"WARN", "WARN"},
            {"ERROR", "ERROR"}
        };
        addField(new ComboFieldEditor(AIAgentPreferences.PREF_LOG_LEVEL, 
            "Log Level:", logLevels, groupComposite));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_ENABLE_CONSOLE_LOGGING, 
            "Enable console logging", groupComposite));
        
        addField(new BooleanFieldEditor(AIAgentPreferences.PREF_LOG_AI_INTERACTIONS, 
            "Log AI interactions (may contain sensitive data)", groupComposite));
    }
    
    private void loadApiKey() {
        try {
            SecureStorage secureStorage = new SecureStorage();
            String providerType = getPreferenceStore().getString(AIAgentPreferences.PREF_AI_PROVIDER_TYPE);
            String apiKey = secureStorage.getApiKey(providerType);
            
            if (apiKey != null && !apiKey.isEmpty()) {
                apiKeyText.setText("••••••••••••••••"); // Show masked key
                connectionStatusLabel.setText("API key is configured");
            } else {
                connectionStatusLabel.setText("No API key configured");
            }
        } catch (Exception e) {
            connectionStatusLabel.setText("Error loading API key: " + e.getMessage());
        }
    }
    
    private void testConnection() {
        // TODO: Implement connection test
        MessageBox messageBox = new MessageBox(getShell(), SWT.ICON_INFORMATION | SWT.OK);
        messageBox.setText("Connection Test");
        messageBox.setMessage("Connection test functionality will be implemented in the AI Provider Client.");
        messageBox.open();
    }
    
    @Override
    public boolean performOk() {
        boolean result = super.performOk();
        
        // Save API key if changed
        String apiKeyValue = apiKeyText.getText();
        if (apiKeyValue != null && !apiKeyValue.equals("••••••••••••••••") && !apiKeyValue.trim().isEmpty()) {
            try {
                SecureStorage secureStorage = new SecureStorage();
                String providerType = getPreferenceStore().getString(AIAgentPreferences.PREF_AI_PROVIDER_TYPE);
                secureStorage.storeApiKey(providerType, apiKeyValue);
                connectionStatusLabel.setText("API key saved successfully");
            } catch (Exception e) {
                connectionStatusLabel.setText("Error saving API key: " + e.getMessage());
            }
        }
        
        return result;
    }
    
    @Override
    public void init(IWorkbench workbench) {
        // Initialize if needed
    }
}
