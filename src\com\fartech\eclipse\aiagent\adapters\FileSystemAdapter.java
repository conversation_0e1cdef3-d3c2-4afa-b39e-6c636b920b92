package com.fartech.eclipse.aiagent.adapters;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.resources.IContainer;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IFolder;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.IResource;
import org.eclipse.core.resources.IWorkspaceRoot;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Adapter for Eclipse file system operations.
 * Provides CRUD operations on files and directories in the workspace.
 */
public class FileSystemAdapter implements IDEAdapter {
    
    private final AIAgentLogger logger;
    private IWorkspaceRoot workspaceRoot;
    private boolean initialized = false;
    
    public FileSystemAdapter() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("FileSystemAdapter already initialized");
            return;
        }
        
        try {
            workspaceRoot = ResourcesPlugin.getWorkspace().getRoot();
            initialized = true;
            logger.info("FileSystemAdapter initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize FileSystemAdapter", e);
            throw e;
        }
    }
    
    @Override
    public boolean isReady() {
        return initialized && workspaceRoot != null;
    }
    
    @Override
    public void shutdown() {
        initialized = false;
        workspaceRoot = null;
        logger.info("FileSystemAdapter shut down");
    }
    
    @Override
    public String getAdapterType() {
        return "filesystem";
    }
    
    @Override
    public String getDescription() {
        return "Eclipse workspace file system operations";
    }
    
    /**
     * Read the contents of a file
     * @param filePath the workspace-relative path to the file
     * @return the file contents as a string
     * @throws Exception if the file cannot be read
     */
    public String readFile(String filePath) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IFile file = workspaceRoot.getFile(new Path(filePath));
            if (!file.exists()) {
                throw new Exception("File does not exist: " + filePath);
            }
            
            InputStream inputStream = file.getContents();
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes);
            inputStream.close();
            
            String content = new String(bytes, file.getCharset());
            logger.debug("Read file: " + filePath + " (" + bytes.length + " bytes)");
            return content;
            
        } catch (Exception e) {
            logger.error("Failed to read file: " + filePath, e);
            throw e;
        }
    }
    
    /**
     * Write content to a file (creates or overwrites)
     * @param filePath the workspace-relative path to the file
     * @param content the content to write
     * @throws Exception if the file cannot be written
     */
    public void writeFile(String filePath, String content) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IFile file = workspaceRoot.getFile(new Path(filePath));
            
            // Create parent directories if they don't exist
            createParentDirectories(file);
            
            InputStream inputStream = new ByteArrayInputStream(content.getBytes("UTF-8"));
            
            if (file.exists()) {
                file.setContents(inputStream, true, true, null);
                logger.debug("Updated file: " + filePath);
            } else {
                file.create(inputStream, true, null);
                logger.debug("Created file: " + filePath);
            }
            
        } catch (Exception e) {
            logger.error("Failed to write file: " + filePath, e);
            throw e;
        }
    }
    
    /**
     * Delete a file
     * @param filePath the workspace-relative path to the file
     * @throws Exception if the file cannot be deleted
     */
    public void deleteFile(String filePath) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IFile file = workspaceRoot.getFile(new Path(filePath));
            if (file.exists()) {
                file.delete(true, null);
                logger.debug("Deleted file: " + filePath);
            } else {
                logger.warn("File does not exist, cannot delete: " + filePath);
            }
            
        } catch (Exception e) {
            logger.error("Failed to delete file: " + filePath, e);
            throw e;
        }
    }
    
    /**
     * Create a directory
     * @param dirPath the workspace-relative path to the directory
     * @throws Exception if the directory cannot be created
     */
    public void createDirectory(String dirPath) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IFolder folder = workspaceRoot.getFolder(new Path(dirPath));
            if (!folder.exists()) {
                createParentDirectories(folder);
                folder.create(true, true, null);
                logger.debug("Created directory: " + dirPath);
            } else {
                logger.debug("Directory already exists: " + dirPath);
            }
            
        } catch (Exception e) {
            logger.error("Failed to create directory: " + dirPath, e);
            throw e;
        }
    }
    
    /**
     * Delete a directory
     * @param dirPath the workspace-relative path to the directory
     * @throws Exception if the directory cannot be deleted
     */
    public void deleteDirectory(String dirPath) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IFolder folder = workspaceRoot.getFolder(new Path(dirPath));
            if (folder.exists()) {
                folder.delete(true, null);
                logger.debug("Deleted directory: " + dirPath);
            } else {
                logger.warn("Directory does not exist, cannot delete: " + dirPath);
            }
            
        } catch (Exception e) {
            logger.error("Failed to delete directory: " + dirPath, e);
            throw e;
        }
    }
    
    /**
     * List files and directories in a directory
     * @param dirPath the workspace-relative path to the directory
     * @return list of resource names in the directory
     * @throws Exception if the directory cannot be listed
     */
    public List<String> listDirectory(String dirPath) throws Exception {
        if (!isReady()) {
            throw new IllegalStateException("FileSystemAdapter not initialized");
        }
        
        try {
            IContainer container;
            if (dirPath == null || dirPath.trim().isEmpty() || dirPath.equals("/")) {
                container = workspaceRoot;
            } else {
                container = workspaceRoot.getFolder(new Path(dirPath));
            }
            
            if (!container.exists()) {
                throw new Exception("Directory does not exist: " + dirPath);
            }
            
            List<String> items = new ArrayList<String>();
            IResource[] members = container.members();
            
            for (IResource member : members) {
                items.add(member.getName());
            }
            
            logger.debug("Listed directory: " + dirPath + " (" + items.size() + " items)");
            return items;
            
        } catch (Exception e) {
            logger.error("Failed to list directory: " + dirPath, e);
            throw e;
        }
    }
    
    /**
     * Check if a file exists
     * @param filePath the workspace-relative path to the file
     * @return true if the file exists, false otherwise
     */
    public boolean fileExists(String filePath) {
        if (!isReady()) {
            return false;
        }
        
        try {
            IFile file = workspaceRoot.getFile(new Path(filePath));
            return file.exists();
        } catch (Exception e) {
            logger.warn("Error checking file existence: " + filePath, e);
            return false;
        }
    }
    
    /**
     * Check if a directory exists
     * @param dirPath the workspace-relative path to the directory
     * @return true if the directory exists, false otherwise
     */
    public boolean directoryExists(String dirPath) {
        if (!isReady()) {
            return false;
        }
        
        try {
            IFolder folder = workspaceRoot.getFolder(new Path(dirPath));
            return folder.exists();
        } catch (Exception e) {
            logger.warn("Error checking directory existence: " + dirPath, e);
            return false;
        }
    }
    
    /**
     * Get all projects in the workspace
     * @return list of project names
     */
    public List<String> getProjects() {
        List<String> projects = new ArrayList<String>();
        
        if (!isReady()) {
            return projects;
        }
        
        try {
            IProject[] workspaceProjects = workspaceRoot.getProjects();
            for (IProject project : workspaceProjects) {
                if (project.isOpen()) {
                    projects.add(project.getName());
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting projects", e);
        }
        
        return projects;
    }
    
    private void createParentDirectories(IResource resource) throws CoreException {
        IContainer parent = resource.getParent();
        if (parent != null && parent.getType() == IResource.FOLDER) {
            IFolder parentFolder = (IFolder) parent;
            if (!parentFolder.exists()) {
                createParentDirectories(parentFolder);
                parentFolder.create(true, true, null);
            }
        }
    }
}
