package com.fartech.eclipse.aiagent.adapters;

import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IWorkspaceRoot;
import org.eclipse.core.resources.ResourcesPlugin;
import org.eclipse.core.runtime.Path;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IEditorInput;
import org.eclipse.ui.IEditorPart;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PlatformUI;
import org.eclipse.ui.ide.IDE;
import org.eclipse.ui.texteditor.IDocumentProvider;
import org.eclipse.ui.texteditor.ITextEditor;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Adapter for Eclipse editor operations.
 * Provides access to active editors and text manipulation.
 */
public class EditorAdapter implements IDEAdapter {
    
    private final AIAgentLogger logger;
    private boolean initialized = false;
    
    public EditorAdapter() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("EditorAdapter already initialized");
            return;
        }
        
        try {
            // Verify that we can access the workbench
            if (PlatformUI.getWorkbench() == null) {
                throw new Exception("Eclipse workbench not available");
            }
            
            initialized = true;
            logger.info("EditorAdapter initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize EditorAdapter", e);
            throw e;
        }
    }
    
    @Override
    public boolean isReady() {
        return initialized;
    }
    
    @Override
    public void shutdown() {
        initialized = false;
        logger.info("EditorAdapter shut down");
    }
    
    @Override
    public String getAdapterType() {
        return "editor";
    }
    
    @Override
    public String getDescription() {
        return "Eclipse editor text operations";
    }
    
    /**
     * Get the currently active editor
     * @return the active editor, or null if none
     */
    public IEditorPart getActiveEditor() {
        if (!isReady()) {
            return null;
        }
        
        try {
            IWorkbenchWindow window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
            if (window != null) {
                IWorkbenchPage page = window.getActivePage();
                if (page != null) {
                    return page.getActiveEditor();
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting active editor", e);
        }
        
        return null;
    }
    
    /**
     * Get the file path of the currently active editor
     * @return the file path, or null if no active editor or not a file editor
     */
    public String getActiveEditorFilePath() {
        IEditorPart editor = getActiveEditor();
        if (editor == null) {
            return null;
        }
        
        try {
            IEditorInput input = editor.getEditorInput();
            // Use reflection to get the file from the editor input
            if (input != null) {
                try {
                    // Try to get the file using the getFile method if it exists
                    java.lang.reflect.Method getFileMethod = input.getClass().getMethod("getFile");
                    Object fileObj = getFileMethod.invoke(input);
                    if (fileObj instanceof IFile) {
                        IFile file = (IFile) fileObj;
                        return file.getFullPath().toString();
                    }
                } catch (Exception reflectionEx) {
                    // If reflection fails, try to get the name from the input
                    return input.getName();
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting active editor file path", e);
        }
        
        return null;
    }
    
    /**
     * Get the content of the currently active editor
     * @return the editor content, or null if no active text editor
     */
    public String getActiveEditorContent() {
        IEditorPart editor = getActiveEditor();
        if (!(editor instanceof ITextEditor)) {
            return null;
        }
        
        try {
            ITextEditor textEditor = (ITextEditor) editor;
            IDocumentProvider provider = textEditor.getDocumentProvider();
            IDocument document = provider.getDocument(textEditor.getEditorInput());
            
            if (document != null) {
                return document.get();
            }
        } catch (Exception e) {
            logger.warn("Error getting active editor content", e);
        }
        
        return null;
    }
    
    /**
     * Get the currently selected text in the active editor
     * @return the selected text, or null if no selection or no active text editor
     */
    public String getSelectedText() {
        IEditorPart editor = getActiveEditor();
        if (!(editor instanceof ITextEditor)) {
            return null;
        }
        
        try {
            ITextEditor textEditor = (ITextEditor) editor;
            ISelection selection = textEditor.getSelectionProvider().getSelection();
            
            if (selection instanceof ITextSelection) {
                ITextSelection textSelection = (ITextSelection) selection;
                return textSelection.getText();
            }
        } catch (Exception e) {
            logger.warn("Error getting selected text", e);
        }
        
        return null;
    }
    
    /**
     * Replace the currently selected text in the active editor
     * @param newText the text to replace the selection with
     * @return true if successful, false otherwise
     */
    public boolean replaceSelectedText(String newText) {
        IEditorPart editor = getActiveEditor();
        if (!(editor instanceof ITextEditor)) {
            return false;
        }
        
        try {
            ITextEditor textEditor = (ITextEditor) editor;
            IDocumentProvider provider = textEditor.getDocumentProvider();
            IDocument document = provider.getDocument(textEditor.getEditorInput());
            ISelection selection = textEditor.getSelectionProvider().getSelection();
            
            if (document != null && selection instanceof ITextSelection) {
                ITextSelection textSelection = (ITextSelection) selection;
                document.replace(textSelection.getOffset(), textSelection.getLength(), newText);
                logger.debug("Replaced selected text in editor");
                return true;
            }
        } catch (BadLocationException e) {
            logger.error("Bad location when replacing text", e);
        } catch (Exception e) {
            logger.warn("Error replacing selected text", e);
        }
        
        return false;
    }
    
    /**
     * Insert text at the current cursor position in the active editor
     * @param text the text to insert
     * @return true if successful, false otherwise
     */
    public boolean insertTextAtCursor(String text) {
        IEditorPart editor = getActiveEditor();
        if (!(editor instanceof ITextEditor)) {
            return false;
        }
        
        try {
            ITextEditor textEditor = (ITextEditor) editor;
            IDocumentProvider provider = textEditor.getDocumentProvider();
            IDocument document = provider.getDocument(textEditor.getEditorInput());
            ISelection selection = textEditor.getSelectionProvider().getSelection();
            
            if (document != null && selection instanceof ITextSelection) {
                ITextSelection textSelection = (ITextSelection) selection;
                document.replace(textSelection.getOffset(), 0, text);
                logger.debug("Inserted text at cursor position");
                return true;
            }
        } catch (BadLocationException e) {
            logger.error("Bad location when inserting text", e);
        } catch (Exception e) {
            logger.warn("Error inserting text at cursor", e);
        }
        
        return false;
    }
    
    /**
     * Set the entire content of the active editor
     * @param content the new content
     * @return true if successful, false otherwise
     */
    public boolean setEditorContent(String content) {
        IEditorPart editor = getActiveEditor();
        if (!(editor instanceof ITextEditor)) {
            return false;
        }
        
        try {
            ITextEditor textEditor = (ITextEditor) editor;
            IDocumentProvider provider = textEditor.getDocumentProvider();
            IDocument document = provider.getDocument(textEditor.getEditorInput());
            
            if (document != null) {
                document.set(content);
                logger.debug("Set editor content");
                return true;
            }
        } catch (Exception e) {
            logger.warn("Error setting editor content", e);
        }
        
        return false;
    }
    
    /**
     * Open a file in an editor
     * @param filePath the workspace-relative path to the file
     * @return true if successful, false otherwise
     */
    public boolean openFile(String filePath) {
        if (!isReady()) {
            return false;
        }
        
        try {
            IWorkspaceRoot root = ResourcesPlugin.getWorkspace().getRoot();
            IFile file = root.getFile(new Path(filePath));
            
            if (!file.exists()) {
                logger.warn("File does not exist: " + filePath);
                return false;
            }
            
            IWorkbenchWindow window = PlatformUI.getWorkbench().getActiveWorkbenchWindow();
            if (window != null) {
                IWorkbenchPage page = window.getActivePage();
                if (page != null) {
                    org.eclipse.ui.ide.IDE.openEditor(page, file);
                    logger.debug("Opened file in editor: " + filePath);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.warn("Error opening file: " + filePath, e);
        }
        
        return false;
    }
    
    /**
     * Save the currently active editor
     * @return true if successful, false otherwise
     */
    public boolean saveActiveEditor() {
        IEditorPart editor = getActiveEditor();
        if (editor == null) {
            return false;
        }
        
        try {
            if (editor.isDirty()) {
                editor.doSave(null);
                logger.debug("Saved active editor");
                return true;
            } else {
                logger.debug("Active editor is not dirty, no save needed");
                return true;
            }
        } catch (Exception e) {
            logger.warn("Error saving active editor", e);
        }
        
        return false;
    }
}
