package com.fartech.eclipse.aiagent.core.logging;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.eclipse.core.runtime.ILog;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Platform;
import org.eclipse.core.runtime.Status;
import org.eclipse.ui.console.ConsolePlugin;
import org.eclipse.ui.console.IConsole;
import org.eclipse.ui.console.IConsoleManager;
import org.eclipse.ui.console.MessageConsole;
import org.eclipse.ui.console.MessageConsoleStream;

import com.fartech.eclipse.aiagent.Activator;

/**
 * Centralized logging system for the AI Agent plugin.
 * Provides both Eclipse log integration and console output for debugging.
 */
public class AIAgentLogger {
    
    private static final String CONSOLE_NAME = "AI Agent";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    
    private final ILog eclipseLog;
    private final MessageConsole console;
    private final MessageConsoleStream consoleStream;
    private final BlockingQueue<LogEntry> logQueue;
    private final Thread loggerThread;
    private volatile boolean shutdown = false;
    
    public AIAgentLogger() {
        this.eclipseLog = Platform.getLog(Activator.getDefault().getBundle());
        this.console = findOrCreateConsole();
        this.consoleStream = console.newMessageStream();
        this.logQueue = new LinkedBlockingQueue<LogEntry>();
        
        // Start background logging thread
        this.loggerThread = new Thread(this::processLogEntries, "AI-Agent-Logger");
        this.loggerThread.setDaemon(true);
        this.loggerThread.start();
    }
    
    public void info(String message) {
        log(LogLevel.INFO, message, null);
    }
    
    public void debug(String message) {
        log(LogLevel.DEBUG, message, null);
    }
    
    public void warn(String message) {
        log(LogLevel.WARN, message, null);
    }
    
    public void warn(String message, Throwable throwable) {
        log(LogLevel.WARN, message, throwable);
    }
    
    public void error(String message) {
        log(LogLevel.ERROR, message, null);
    }
    
    public void error(String message, Throwable throwable) {
        log(LogLevel.ERROR, message, throwable);
    }
    
    private void log(LogLevel level, String message, Throwable throwable) {
        if (shutdown) {
            return;
        }
        
        LogEntry entry = new LogEntry(level, message, throwable, System.currentTimeMillis());
        try {
            logQueue.offer(entry);
        } catch (Exception e) {
            // Fallback to direct logging if queue fails
            logDirectly(entry);
        }
    }
    
    private void processLogEntries() {
        while (!shutdown) {
            try {
                LogEntry entry = logQueue.take();
                logDirectly(entry);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                // Continue processing other entries
                System.err.println("Error processing log entry: " + e.getMessage());
            }
        }
    }
    
    private void logDirectly(LogEntry entry) {
        // Log to Eclipse log
        int severity = getSeverity(entry.level);
        IStatus status = new Status(severity, Activator.PLUGIN_ID, entry.message, entry.throwable);
        eclipseLog.log(status);
        
        // Log to console
        String timestamp = DATE_FORMAT.format(new Date(entry.timestamp));
        String logLine = String.format("[%s] %s: %s", timestamp, entry.level, entry.message);
        
        try {
            consoleStream.println(logLine);
            if (entry.throwable != null) {
                entry.throwable.printStackTrace(consoleStream);
            }
        } catch (Exception e) {
            // Ignore console errors
        }
    }
    
    private int getSeverity(LogLevel level) {
        switch (level) {
            case ERROR:
                return IStatus.ERROR;
            case WARN:
                return IStatus.WARNING;
            case INFO:
                return IStatus.INFO;
            case DEBUG:
            default:
                return IStatus.OK;
        }
    }
    
    private MessageConsole findOrCreateConsole() {
        IConsoleManager consoleManager = ConsolePlugin.getDefault().getConsoleManager();
        IConsole[] consoles = consoleManager.getConsoles();
        
        for (IConsole console : consoles) {
            if (CONSOLE_NAME.equals(console.getName())) {
                return (MessageConsole) console;
            }
        }
        
        // Create new console
        MessageConsole newConsole = new MessageConsole(CONSOLE_NAME, null);
        consoleManager.addConsoles(new IConsole[] { newConsole });
        return newConsole;
    }
    
    public void shutdown() {
        shutdown = true;
        if (loggerThread != null) {
            loggerThread.interrupt();
        }
        
        try {
            if (consoleStream != null) {
                consoleStream.close();
            }
        } catch (IOException e) {
            // Ignore
        }
    }
    
    private static class LogEntry {
        final LogLevel level;
        final String message;
        final Throwable throwable;
        final long timestamp;
        
        LogEntry(LogLevel level, String message, Throwable throwable, long timestamp) {
            this.level = level;
            this.message = message;
            this.throwable = throwable;
            this.timestamp = timestamp;
        }
    }
    
    private enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
}
