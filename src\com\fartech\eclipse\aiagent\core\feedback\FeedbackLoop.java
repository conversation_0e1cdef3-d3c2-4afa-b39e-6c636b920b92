package com.fartech.eclipse.aiagent.core.feedback;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.context.ContextManager;
import com.fartech.eclipse.aiagent.core.execution.ExecutionOrchestrator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.planning.ExecutionPlan;
import com.fartech.eclipse.aiagent.core.planning.ExecutionStep;
import com.fartech.eclipse.aiagent.core.planning.PlanningEngine;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.providers.AIProviderClient;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;

/**
 * Feedback loop for self-correction and learning.
 * Analyzes execution results and provides feedback for improvement.
 */
public class FeedbackLoop {
    
    private final AIAgentLogger logger;
    private final ContextManager contextManager;
    private final PlanningEngine planningEngine;
    private final AIProviderClient aiProviderClient;
    
    private ExecutionOrchestrator executionOrchestrator;
    private boolean initialized = false;
    
    public FeedbackLoop(ContextManager contextManager, PlanningEngine planningEngine, AIProviderClient aiProviderClient) {
        this.logger = Activator.getDefault().getLogger();
        this.contextManager = contextManager;
        this.planningEngine = planningEngine;
        this.aiProviderClient = aiProviderClient;
    }
    
    /**
     * Initialize the feedback loop
     */
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("FeedbackLoop already initialized");
            return;
        }
        
        try {
            logger.info("Initializing FeedbackLoop...");
            
            // Verify dependencies
            if (contextManager == null) {
                throw new Exception("ContextManager is required");
            }
            
            if (planningEngine == null) {
                throw new Exception("PlanningEngine is required");
            }
            
            if (aiProviderClient == null) {
                throw new Exception("AIProviderClient is required");
            }
            
            initialized = true;
            logger.info("FeedbackLoop initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize FeedbackLoop", e);
            throw e;
        }
    }
    
    /**
     * Set the execution orchestrator (called by AIAgentCore)
     */
    public void setExecutionOrchestrator(ExecutionOrchestrator executionOrchestrator) {
        this.executionOrchestrator = executionOrchestrator;
    }
    
    /**
     * Handle a step failure and attempt self-correction
     * @param failedStep the step that failed
     * @param plan the current execution plan
     */
    public void handleStepFailure(ExecutionStep failedStep, ExecutionPlan plan) {
        if (!initialized) {
            logger.warn("FeedbackLoop not initialized, cannot handle step failure");
            return;
        }
        
        logger.info("Handling step failure: " + failedStep.getOperation());
        
        try {
            // Check if self-correction is enabled
            boolean selfCorrectionEnabled = AIAgentPreferences.getPreferenceStore()
                .getBoolean(AIAgentPreferences.PREF_ENABLE_SELF_CORRECTION);
            
            if (!selfCorrectionEnabled) {
                logger.info("Self-correction disabled, skipping feedback analysis");
                return;
            }
            
            // Analyze the failure
            String analysis = analyzeFailure(failedStep, plan);
            
            // Attempt to generate a corrective action
            String correction = generateCorrection(failedStep, analysis);
            
            if (correction != null && !correction.trim().isEmpty()) {
                logger.info("Generated correction: " + correction);
                
                // Try to re-plan if possible
                int maxRetries = AIAgentPreferences.getPreferenceStore()
                    .getInt(AIAgentPreferences.PREF_MAX_RETRY_ATTEMPTS);
                
                if (getRetryCount(plan) < maxRetries) {
                    attemptReplan(plan, correction);
                } else {
                    logger.warn("Maximum retry attempts reached, stopping execution");
                }
            } else {
                logger.warn("Could not generate correction for failed step");
            }
            
        } catch (Exception e) {
            logger.error("Error in feedback loop handling step failure", e);
        }
    }
    
    /**
     * Handle a general execution error
     * @param error the error that occurred
     */
    public void handleExecutionError(Exception error) {
        if (!initialized) {
            return;
        }
        
        logger.info("Handling execution error: " + error.getMessage());
        
        try {
            // Log the error for analysis
            String errorAnalysis = analyzeExecutionError(error);
            logger.info("Error analysis: " + errorAnalysis);
            
            // Add to context for future reference
            contextManager.setContextValue("last.execution.error", error.getMessage());
            contextManager.setContextValue("last.error.analysis", errorAnalysis);
            
        } catch (Exception e) {
            logger.error("Error analyzing execution error", e);
        }
    }
    
    private String analyzeFailure(ExecutionStep failedStep, ExecutionPlan plan) {
        try {
            StringBuilder analysisPrompt = new StringBuilder();
            
            analysisPrompt.append("Analyze this execution failure and provide insights:\n\n");
            analysisPrompt.append("Failed Step: ").append(failedStep.getOperation()).append("\n");
            analysisPrompt.append("Error Message: ").append(failedStep.getErrorMessage()).append("\n");
            analysisPrompt.append("Step Type: ").append(failedStep.getActionType()).append("\n");
            analysisPrompt.append("Expected Outcome: ").append(failedStep.getExpectedOutcome()).append("\n\n");
            
            analysisPrompt.append("Current Context:\n");
            analysisPrompt.append(contextManager.buildContextSummary());
            analysisPrompt.append("\n");
            
            analysisPrompt.append("Plan Summary:\n");
            analysisPrompt.append(plan.getSummary());
            analysisPrompt.append("\n\n");
            
            analysisPrompt.append("Please analyze what went wrong and suggest potential causes:");
            
            AIRequest request = new AIRequest(analysisPrompt.toString());
            request.setSystemMessage("You are an expert software development assistant analyzing execution failures. Provide concise, actionable insights.");
            request.setMaxTokens(1000);
            request.setTemperature(0.3);
            
            AIResponse response = aiProviderClient.sendRequest(request);
            
            if (response.isSuccess()) {
                return response.getContent();
            } else {
                logger.warn("Failed to get AI analysis: " + response.getErrorMessage());
                return "Analysis failed: " + response.getErrorMessage();
            }
            
        } catch (Exception e) {
            logger.error("Error analyzing failure", e);
            return "Analysis error: " + e.getMessage();
        }
    }
    
    private String generateCorrection(ExecutionStep failedStep, String analysis) {
        try {
            StringBuilder correctionPrompt = new StringBuilder();
            
            correctionPrompt.append("Generate a correction for this failed execution step:\n\n");
            correctionPrompt.append("Failed Step: ").append(failedStep.getOperation()).append("\n");
            correctionPrompt.append("Error: ").append(failedStep.getErrorMessage()).append("\n");
            correctionPrompt.append("Analysis: ").append(analysis).append("\n\n");
            
            correctionPrompt.append("Current Context:\n");
            correctionPrompt.append(contextManager.buildContextSummary());
            correctionPrompt.append("\n\n");
            
            correctionPrompt.append("Please provide a specific corrective action or alternative approach:");
            
            AIRequest request = new AIRequest(correctionPrompt.toString());
            request.setSystemMessage("You are an expert software development assistant. Provide specific, actionable corrections for failed execution steps.");
            request.setMaxTokens(800);
            request.setTemperature(0.4);
            
            AIResponse response = aiProviderClient.sendRequest(request);
            
            if (response.isSuccess()) {
                return response.getContent();
            } else {
                logger.warn("Failed to generate correction: " + response.getErrorMessage());
                return null;
            }
            
        } catch (Exception e) {
            logger.error("Error generating correction", e);
            return null;
        }
    }
    
    private String analyzeExecutionError(Exception error) {
        StringBuilder analysis = new StringBuilder();
        
        analysis.append("Execution Error Analysis:\n");
        analysis.append("Error Type: ").append(error.getClass().getSimpleName()).append("\n");
        analysis.append("Message: ").append(error.getMessage()).append("\n");
        
        // Categorize common error types
        String message = error.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            
            if (lowerMessage.contains("file not found") || lowerMessage.contains("no such file")) {
                analysis.append("Category: File System Error - Missing File\n");
                analysis.append("Suggestion: Verify file paths and ensure files exist\n");
            } else if (lowerMessage.contains("permission denied") || lowerMessage.contains("access denied")) {
                analysis.append("Category: Permission Error\n");
                analysis.append("Suggestion: Check file/directory permissions\n");
            } else if (lowerMessage.contains("timeout") || lowerMessage.contains("timed out")) {
                analysis.append("Category: Timeout Error\n");
                analysis.append("Suggestion: Increase timeout or optimize operation\n");
            } else if (lowerMessage.contains("network") || lowerMessage.contains("connection")) {
                analysis.append("Category: Network Error\n");
                analysis.append("Suggestion: Check network connectivity and endpoints\n");
            } else {
                analysis.append("Category: General Error\n");
                analysis.append("Suggestion: Review error details and context\n");
            }
        }
        
        return analysis.toString();
    }
    
    private void attemptReplan(ExecutionPlan plan, String correction) {
        try {
            logger.info("Attempting to re-plan based on correction");
            
            String feedback = "Previous execution failed. Correction: " + correction;
            planningEngine.replan(feedback, plan.getOriginalGoal());
            
            // Increment retry count
            incrementRetryCount(plan);
            
            logger.info("Re-planning completed, execution will continue with new plan");
            
        } catch (Exception e) {
            logger.error("Failed to re-plan", e);
        }
    }
    
    private int getRetryCount(ExecutionPlan plan) {
        Object retryCount = contextManager.getContextValue("plan.retryCount." + plan.hashCode());
        return retryCount instanceof Integer ? (Integer) retryCount : 0;
    }
    
    private void incrementRetryCount(ExecutionPlan plan) {
        int currentCount = getRetryCount(plan);
        contextManager.setContextValue("plan.retryCount." + plan.hashCode(), currentCount + 1);
    }
    
    /**
     * Provide feedback on successful execution
     * @param plan the completed execution plan
     */
    public void handleSuccessfulExecution(ExecutionPlan plan) {
        if (!initialized) {
            return;
        }
        
        logger.info("Handling successful execution of plan: " + plan.getOriginalGoal());
        
        try {
            // Record successful patterns for future learning
            contextManager.setContextValue("last.successful.plan", plan.getSummary());
            contextManager.setContextValue("last.success.timestamp", System.currentTimeMillis());
            
            // Add success to conversation history
            contextManager.addAIResponse("Successfully completed: " + plan.getOriginalGoal());
            
        } catch (Exception e) {
            logger.error("Error handling successful execution", e);
        }
    }
    
    /**
     * Shutdown the feedback loop
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        logger.info("Shutting down FeedbackLoop...");
        
        initialized = false;
        
        logger.info("FeedbackLoop shut down successfully");
    }
}
