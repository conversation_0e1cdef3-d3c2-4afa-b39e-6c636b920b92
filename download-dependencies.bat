@echo off
REM Download dependencies for Eclipse AI Agent Plugin
REM This script downloads the required JAR files from Maven Central

echo ========================================
echo Downloading Eclipse AI Agent Dependencies
echo ========================================

REM Create lib directory if it doesn't exist
if not exist "lib" mkdir lib

REM Check if curl is available
where curl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: curl is not installed or not in PATH
    echo Please install curl or download the JAR files manually
    echo See lib/README.md for download URLs
    pause
    exit /b 1
)

echo Downloading dependencies from Maven Central...
echo.

REM Download Apache HttpClient 4.5.14
echo [1/4] Downloading httpclient-4.5.14.jar...
curl -L -o "lib/httpclient-4.5.14.jar" "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download httpclient-4.5.14.jar
    goto :error
)

REM Download Apache HttpCore 4.4.16
echo [2/4] Downloading httpcore-4.4.16.jar...
curl -L -o "lib/httpcore-4.4.16.jar" "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download httpcore-4.4.16.jar
    goto :error
)

REM Download Gson 2.10.1
echo [3/4] Downloading gson-2.10.1.jar...
curl -L -o "lib/gson-2.10.1.jar" "https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download gson-2.10.1.jar
    goto :error
)

REM Download Commons Logging 1.2
echo [4/4] Downloading commons-logging-1.2.jar...
curl -L -o "lib/commons-logging-1.2.jar" "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download commons-logging-1.2.jar
    goto :error
)

echo.
echo ========================================
echo Dependencies downloaded successfully!
echo ========================================
echo.
echo Downloaded files:
dir /b lib\*.jar
echo.
echo You can now refresh your Eclipse project to resolve build path errors.
echo Go to Project > Refresh or press F5 in Eclipse.
echo ========================================

pause
exit /b 0

:error
echo.
echo ========================================
echo Download failed!
echo ========================================
echo.
echo Please check your internet connection and try again.
echo Alternatively, download the JAR files manually from:
echo https://repo1.maven.org/maven2/
echo.
echo See lib/README.md for detailed download instructions.
echo ========================================
pause
exit /b 1
