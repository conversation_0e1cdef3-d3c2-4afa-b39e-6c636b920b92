package com.fartech.eclipse.aiagent.core;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fartech.eclipse.aiagent.core.context.ContextManager;
import com.fartech.eclipse.aiagent.core.execution.ExecutionOrchestrator;
import com.fartech.eclipse.aiagent.core.feedback.FeedbackLoop;
import com.fartech.eclipse.aiagent.core.planning.PlanningEngine;
import com.fartech.eclipse.aiagent.core.security.SecureStorage;
import com.fartech.eclipse.aiagent.providers.AIProviderClient;

/**
 * Unit tests for AIAgentCore.
 */
public class AIAgentCoreTest {
    
    @Mock
    private ContextManager mockContextManager;
    
    @Mock
    private PlanningEngine mockPlanningEngine;
    
    @Mock
    private ExecutionOrchestrator mockExecutionOrchestrator;
    
    @Mock
    private FeedbackLoop mockFeedbackLoop;
    
    @Mock
    private AIProviderClient mockAIProviderClient;
    
    @Mock
    private SecureStorage mockSecureStorage;
    
    private AIAgentCore aiAgentCore;
    
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // Mock successful initialization
        doNothing().when(mockContextManager).initialize();
        doNothing().when(mockAIProviderClient).initialize();
        doNothing().when(mockPlanningEngine).initialize();
        doNothing().when(mockExecutionOrchestrator).initialize();
        doNothing().when(mockFeedbackLoop).initialize();
        
        // Create AIAgentCore instance
        aiAgentCore = new AIAgentCore();
    }
    
    @Test
    public void testInitialization() throws Exception {
        // Test that initialization completes without errors
        aiAgentCore.initialize();
        
        // Verify that all components are initialized
        verify(mockContextManager).initialize();
        verify(mockAIProviderClient).initialize();
        verify(mockPlanningEngine).initialize();
        verify(mockExecutionOrchestrator).initialize();
        verify(mockFeedbackLoop).initialize();
    }
    
    @Test
    public void testInitializationFailure() throws Exception {
        // Mock initialization failure
        doThrow(new Exception("Initialization failed")).when(mockContextManager).initialize();
        
        try {
            aiAgentCore.initialize();
            fail("Expected exception was not thrown");
        } catch (Exception e) {
            assertEquals("Initialization failed", e.getMessage());
        }
    }
    
    @Test
    public void testProcessUserRequest() throws Exception {
        // Initialize first
        aiAgentCore.initialize();
        
        String userInput = "Create a new Java class";
        
        // Mock context manager behavior
        doNothing().when(mockContextManager).setUserInput(userInput);
        
        // Mock planning engine behavior
        doNothing().when(mockPlanningEngine).generatePlan(userInput);
        when(mockPlanningEngine.getCurrentPlan()).thenReturn(null);
        
        // Mock execution orchestrator behavior
        doNothing().when(mockExecutionOrchestrator).executePlan(any());
        
        // Process the request
        aiAgentCore.processUserRequest(userInput);
        
        // Verify interactions
        verify(mockContextManager).setUserInput(userInput);
        verify(mockPlanningEngine).generatePlan(userInput);
    }
    
    @Test
    public void testProcessUserRequestWithoutInitialization() {
        String userInput = "Create a new Java class";
        
        try {
            aiAgentCore.processUserRequest(userInput);
            fail("Expected IllegalStateException was not thrown");
        } catch (IllegalStateException e) {
            assertEquals("AI Agent Core not initialized", e.getMessage());
        }
    }
    
    @Test
    public void testCancelCurrentTask() throws Exception {
        // Initialize first
        aiAgentCore.initialize();
        
        // Test cancellation
        aiAgentCore.cancelCurrentTask();
        
        // Verify that orchestrator and planning engine are notified
        verify(mockExecutionOrchestrator).cancelCurrentExecution();
        verify(mockPlanningEngine).cancelCurrentPlanning();
    }
    
    @Test
    public void testGetCurrentStatus() throws Exception {
        // Initialize first
        aiAgentCore.initialize();
        
        // Mock status
        when(mockExecutionOrchestrator.getCurrentStatus()).thenReturn("Executing step 1");
        
        String status = aiAgentCore.getCurrentStatus();
        assertEquals("Idle", status); // Should be idle when no task is running
    }
    
    @Test
    public void testShutdown() throws Exception {
        // Initialize first
        aiAgentCore.initialize();
        
        // Test shutdown
        aiAgentCore.shutdown();
        
        // Verify that all components are shut down
        verify(mockFeedbackLoop).shutdown();
        verify(mockExecutionOrchestrator).shutdown();
        verify(mockPlanningEngine).shutdown();
        verify(mockAIProviderClient).shutdown();
        verify(mockContextManager).shutdown();
    }
    
    @Test
    public void testMultipleInitialization() throws Exception {
        // Test that multiple initialization calls are handled gracefully
        aiAgentCore.initialize();
        aiAgentCore.initialize(); // Should not throw exception
        
        // Verify that components are only initialized once
        verify(mockContextManager, times(1)).initialize();
    }
    
    @Test
    public void testShutdownWithoutInitialization() {
        // Test that shutdown without initialization doesn't cause errors
        aiAgentCore.shutdown(); // Should not throw exception
    }
}
