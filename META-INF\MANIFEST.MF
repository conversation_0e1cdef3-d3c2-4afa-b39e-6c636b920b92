Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Eclipse AI Agent Plugin
<PERSON>le-SymbolicName: com.fartech.eclipse.aiagent;singleton:=true
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.fartech.eclipse.aiagent.Activator
Bundle-Vendor: FarTech
Automatic-Module-Name: com.fartech.eclipse.aiagent
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 org.eclipse.core.resources,
 org.eclipse.ui.editors,
 org.eclipse.ui.workbench.texteditor,
 org.eclipse.jface.text,
 org.eclipse.compare,
 org.eclipse.compare.core,
 org.eclipse.ui.console,
 org.eclipse.debug.core,
 org.eclipse.debug.ui,
 org.eclipse.jdt.core,
 org.eclipse.jdt.ui,
 org.eclipse.jdt.launching,
 org.eclipse.jdt.junit,
 org.eclipse.m2e.core,
 org.eclipse.m2e.maven.runtime,
 org.eclipse.buildship.core,
 org.eclipse.jgit,
 org.eclipse.equinox.security
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Bundle-ActivationPolicy: lazy
Export-Package: com.fartech.eclipse.aiagent.api
Bundle-ClassPath: .,
 lib/httpclient-4.5.14.jar,
 lib/httpcore-4.4.16.jar,
 lib/gson-2.10.1.jar,
 lib/commons-logging-1.2.jar
