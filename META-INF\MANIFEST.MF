Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Eclipse AI Agent Plugin
Bundle-SymbolicName: com.fartech.eclipse.aiagent;singleton:=true
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.fartech.eclipse.aiagent.Activator
Bundle-Vendor: FarTech
Automatic-Module-Name: com.fartech.eclipse.aiagent
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 org.eclipse.core.resources,
 org.eclipse.ui.editors,
 org.eclipse.ui.workbench.texteditor,
 org.eclipse.ui.ide,
 org.eclipse.jface.text,
 org.eclipse.compare,
 org.eclipse.compare.core,
 org.eclipse.ui.console,
 org.eclipse.debug.core;resolution:=optional,
 org.eclipse.debug.ui;resolution:=optional,
 org.eclipse.jdt.core;resolution:=optional,
 org.eclipse.jdt.ui;resolution:=optional,
 org.eclipse.jdt.launching;resolution:=optional,
 org.eclipse.jdt.junit;resolution:=optional,
 org.eclipse.m2e.core;resolution:=optional,
 org.eclipse.m2e.maven.runtime;resolution:=optional,
 org.eclipse.buildship.core;resolution:=optional,
 org.eclipse.jgit;resolution:=optional,
 org.eclipse.equinox.security
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Bundle-ActivationPolicy: lazy
Export-Package: com.fartech.eclipse.aiagent.api
Bundle-ClassPath: .,
 lib/httpclient-4.5.14.jar,
 lib/httpcore-4.4.16.jar,
 lib/gson-2.10.1.jar,
 lib/commons-logging-1.2.jar
