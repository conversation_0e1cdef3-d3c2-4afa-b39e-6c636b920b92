package com.fartech.eclipse.aiagent.api;

import java.util.concurrent.Future;

/**
 * Public API interface for the AI Agent service.
 * Other plugins can use this interface to interact with the AI Agent.
 * 
 * @since 1.0.0
 */
public interface IAIAgentService {
    
    /**
     * Process a user request asynchronously.
     * 
     * @param userInput the user's natural language input
     * @return a Future representing the task execution
     * @throws IllegalStateException if the AI Agent is not initialized
     */
    Future<?> processUserRequest(String userInput);
    
    /**
     * Cancel the currently running task.
     */
    void cancelCurrentTask();
    
    /**
     * Check if a task is currently running.
     * 
     * @return true if a task is running, false otherwise
     */
    boolean isTaskRunning();
    
    /**
     * Get the current execution status.
     * 
     * @return a string describing the current status
     */
    String getCurrentStatus();
    
    /**
     * Check if the AI Agent is ready to process requests.
     * 
     * @return true if ready, false otherwise
     */
    boolean isReady();
}
