package com.fartech.eclipse.aiagent.core.planning;

/**
 * Represents a single step in an execution plan.
 */
public class ExecutionStep {
    
    private final int stepNumber;
    private final String actionType;
    private final String operation;
    private final String description;
    private final String expectedOutcome;
    
    private ExecutionStatus status;
    private String result;
    private String errorMessage;
    private long startTime;
    private long endTime;
    
    public ExecutionStep(int stepNumber, String actionType, String operation, String description, String expectedOutcome) {
        this.stepNumber = stepNumber;
        this.actionType = actionType != null ? actionType : "General";
        this.operation = operation != null ? operation : "";
        this.description = description != null ? description : operation;
        this.expectedOutcome = expectedOutcome != null ? expectedOutcome : "Step completed";
        this.status = ExecutionStatus.PENDING;
    }
    
    /**
     * Get the step number
     */
    public int getStepNumber() {
        return stepNumber;
    }
    
    /**
     * Get the action type (e.g., "FileSystem", "Editor", "Terminal")
     */
    public String getActionType() {
        return actionType;
    }
    
    /**
     * Get the operation to perform
     */
    public String getOperation() {
        return operation;
    }
    
    /**
     * Get the step description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Get the expected outcome
     */
    public String getExpectedOutcome() {
        return expectedOutcome;
    }
    
    /**
     * Get the execution status
     */
    public ExecutionStatus getStatus() {
        return status;
    }
    
    /**
     * Set the execution status
     */
    public void setStatus(ExecutionStatus status) {
        this.status = status;
    }
    
    /**
     * Get the execution result
     */
    public String getResult() {
        return result;
    }
    
    /**
     * Set the execution result
     */
    public void setResult(String result) {
        this.result = result;
    }
    
    /**
     * Get the error message (if any)
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * Set the error message
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * Get the start time
     */
    public long getStartTime() {
        return startTime;
    }
    
    /**
     * Set the start time
     */
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    /**
     * Get the end time
     */
    public long getEndTime() {
        return endTime;
    }
    
    /**
     * Set the end time
     */
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    /**
     * Get the execution duration in milliseconds
     */
    public long getDurationMs() {
        if (startTime > 0 && endTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }
    
    /**
     * Mark the step as started
     */
    public void markStarted() {
        this.status = ExecutionStatus.RUNNING;
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * Mark the step as completed successfully
     */
    public void markCompleted(String result) {
        this.status = ExecutionStatus.COMPLETED;
        this.result = result;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * Mark the step as failed
     */
    public void markFailed(String errorMessage) {
        this.status = ExecutionStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * Mark the step as skipped
     */
    public void markSkipped(String reason) {
        this.status = ExecutionStatus.SKIPPED;
        this.result = reason;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * Check if the step requires user approval
     */
    public boolean requiresApproval() {
        // Steps that modify files or execute potentially dangerous commands require approval
        String lowerAction = actionType.toLowerCase();
        String lowerOp = operation.toLowerCase();
        
        return lowerOp.contains("delete") || 
               lowerOp.contains("remove") || 
               lowerOp.contains("drop") ||
               lowerAction.equals("terminal") ||
               lowerOp.contains("write") ||
               lowerOp.contains("create");
    }
    
    /**
     * Get a status summary
     */
    public String getStatusSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Step ").append(stepNumber).append(": ").append(status);
        
        if (status == ExecutionStatus.RUNNING && startTime > 0) {
            long elapsed = System.currentTimeMillis() - startTime;
            summary.append(" (").append(elapsed).append("ms elapsed)");
        } else if (status == ExecutionStatus.COMPLETED || status == ExecutionStatus.FAILED) {
            summary.append(" (").append(getDurationMs()).append("ms)");
        }
        
        if (errorMessage != null) {
            summary.append(" - Error: ").append(errorMessage);
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return "ExecutionStep{" +
                "stepNumber=" + stepNumber +
                ", actionType='" + actionType + '\'' +
                ", operation='" + operation + '\'' +
                ", status=" + status +
                ", duration=" + getDurationMs() + "ms" +
                '}';
    }
    
    /**
     * Execution status enumeration
     */
    public enum ExecutionStatus {
        PENDING,    // Not yet started
        RUNNING,    // Currently executing
        COMPLETED,  // Successfully completed
        FAILED,     // Failed with error
        SKIPPED     // Skipped (e.g., user declined approval)
    }
}
