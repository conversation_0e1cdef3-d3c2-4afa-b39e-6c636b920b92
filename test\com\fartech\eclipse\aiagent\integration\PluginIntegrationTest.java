package com.fartech.eclipse.aiagent.integration;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.core.security.SecureStorage;
import com.fartech.eclipse.aiagent.providers.OpenAIHandler;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.google.gson.Gson;

/**
 * Integration tests for the Eclipse AI Agent Plugin.
 * These tests verify that components work together correctly.
 * 
 * Note: These tests require a running Eclipse platform for full integration testing.
 * In a CI environment, they can be run with mock Eclipse platform.
 */
public class PluginIntegrationTest {
    
    private Gson gson;
    
    @Before
    public void setUp() {
        gson = new Gson();
    }
    
    @Test
    public void testPreferencesIntegration() {
        // Test that preference constants are properly defined
        assertNotNull(AIAgentPreferences.PREF_AI_PROVIDER_TYPE);
        assertNotNull(AIAgentPreferences.PREF_AI_PROVIDER_ENDPOINT);
        assertNotNull(AIAgentPreferences.PREF_AI_PROVIDER_MODEL);
        
        // Test default values
        assertEquals("openai", AIAgentPreferences.DEFAULT_AI_PROVIDER_TYPE);
        assertEquals("https://api.openai.com/v1/chat/completions", AIAgentPreferences.DEFAULT_AI_PROVIDER_ENDPOINT);
        assertEquals("gpt-4", AIAgentPreferences.DEFAULT_AI_PROVIDER_MODEL);
        
        // Test security preferences
        assertTrue(AIAgentPreferences.DEFAULT_REQUIRE_APPROVAL);
        assertFalse(AIAgentPreferences.DEFAULT_ENABLE_DATA_SCRUBBING);
        
        // Test agent behavior preferences
        assertTrue(AIAgentPreferences.DEFAULT_ENABLE_AUTONOMOUS_MODE);
        assertEquals(10, AIAgentPreferences.DEFAULT_MAX_EXECUTION_STEPS);
        assertTrue(AIAgentPreferences.DEFAULT_ENABLE_SELF_CORRECTION);
        assertEquals(3, AIAgentPreferences.DEFAULT_MAX_RETRY_ATTEMPTS);
    }
    
    @Test
    public void testSecureStorageValidation() {
        // Test API key validation logic
        assertTrue(SecureStorage.isValidApiKey("sk-1234567890abcdef1234567890abcdef1234567890abcdef"));
        assertTrue(SecureStorage.isValidApiKey("valid-long-api-key-12345"));
        
        assertFalse(SecureStorage.isValidApiKey(null));
        assertFalse(SecureStorage.isValidApiKey(""));
        assertFalse(SecureStorage.isValidApiKey("short"));
        assertFalse(SecureStorage.isValidApiKey("test"));
        assertFalse(SecureStorage.isValidApiKey("example"));
        assertFalse(SecureStorage.isValidApiKey("placeholder"));
        assertFalse(SecureStorage.isValidApiKey("your-api-key"));
    }
    
    @Test
    public void testOpenAIHandlerIntegration() {
        OpenAIHandler handler = new OpenAIHandler(gson, null);
        
        // Test provider information
        assertEquals("openai", handler.getProviderType());
        assertEquals("gpt-4", handler.getDefaultModel());
        assertEquals(8192, handler.getMaxTokens());
        
        // Test API key validation
        assertTrue(handler.validateApiKey("sk-1234567890abcdef1234567890abcdef1234567890abcdef"));
        assertFalse(handler.validateApiKey("invalid-key"));
        assertFalse(handler.validateApiKey(null));
        assertFalse(handler.validateApiKey(""));
    }
    
    @Test
    public void testAIRequestCreation() {
        // Test basic request creation
        AIRequest request = new AIRequest("Test prompt");
        assertEquals("Test prompt", request.getPrompt());
        assertEquals(4000, request.getMaxTokens());
        assertEquals(0.7, request.getTemperature(), 0.001);
        assertFalse(request.isStream());
        
        // Test request with parameters
        AIRequest customRequest = new AIRequest("Custom prompt", 1000, 0.5);
        assertEquals("Custom prompt", customRequest.getPrompt());
        assertEquals(1000, customRequest.getMaxTokens());
        assertEquals(0.5, customRequest.getTemperature(), 0.001);
        
        // Test context addition
        customRequest.addContext("Context item 1");
        customRequest.addContext("Context item 2");
        assertEquals(2, customRequest.getContext().size());
        
        // Test system message
        customRequest.setSystemMessage("You are a helpful assistant");
        assertEquals("You are a helpful assistant", customRequest.getSystemMessage());
        
        // Test complete prompt building
        String completePrompt = customRequest.buildCompletePrompt();
        assertTrue(completePrompt.contains("System: You are a helpful assistant"));
        assertTrue(completePrompt.contains("Context:"));
        assertTrue(completePrompt.contains("- Context item 1"));
        assertTrue(completePrompt.contains("- Context item 2"));
        assertTrue(completePrompt.contains("User: Custom prompt"));
    }
    
    @Test
    public void testProviderHandlerFactory() {
        // Test that we can identify different provider types
        String[] supportedProviders = {"openai", "anthropic", "azure", "local", "custom"};
        
        for (String providerType : supportedProviders) {
            // In a real implementation, we would test the factory method
            // For now, just verify the provider types are recognized
            assertTrue("Provider type should be supported: " + providerType, 
                      providerType.length() > 0);
        }
    }
    
    @Test
    public void testConfigurationConsistency() {
        // Test that configuration values are consistent across components
        
        // Default timeout should be reasonable
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_TIMEOUT >= 1000);
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_TIMEOUT <= 300000);
        
        // Default max tokens should be reasonable
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_MAX_TOKENS >= 100);
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_MAX_TOKENS <= 32000);
        
        // Default temperature should be in valid range
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_TEMPERATURE >= 0.0);
        assertTrue(AIAgentPreferences.DEFAULT_AI_PROVIDER_TEMPERATURE <= 2.0);
        
        // Max execution steps should be reasonable
        assertTrue(AIAgentPreferences.DEFAULT_MAX_EXECUTION_STEPS >= 1);
        assertTrue(AIAgentPreferences.DEFAULT_MAX_EXECUTION_STEPS <= 50);
        
        // Max retry attempts should be reasonable
        assertTrue(AIAgentPreferences.DEFAULT_MAX_RETRY_ATTEMPTS >= 1);
        assertTrue(AIAgentPreferences.DEFAULT_MAX_RETRY_ATTEMPTS <= 10);
    }
    
    @Test
    public void testJava8Compatibility() {
        // Test that we're not using Java 9+ features
        
        // Test that we can create basic objects without issues
        AIRequest request = new AIRequest("Test");
        assertNotNull(request);
        
        // Test that Gson works with our models
        String json = gson.toJson(request);
        assertNotNull(json);
        assertTrue(json.contains("Test"));
        
        // Test that we can use Java 8 streams (but not var keyword)
        java.util.List<String> items = java.util.Arrays.asList("item1", "item2", "item3");
        long count = items.stream().filter(item -> item.startsWith("item")).count();
        assertEquals(3, count);
    }
    
    @Test
    public void testErrorHandling() {
        // Test that error handling is robust
        
        // Test null handling in SecureStorage
        assertFalse(SecureStorage.isValidApiKey(null));
        
        // Test empty string handling
        assertFalse(SecureStorage.isValidApiKey(""));
        
        // Test whitespace handling
        assertFalse(SecureStorage.isValidApiKey("   "));
        
        // Test that we handle edge cases gracefully
        AIRequest request = new AIRequest("");
        assertEquals("", request.getPrompt());
        
        request.setContext(null);
        assertNotNull(request.getContext());
        assertEquals(0, request.getContext().size());
    }
    
    @Test
    public void testPluginConstants() {
        // Test that plugin constants are properly defined
        
        // These would be imported from the actual plugin classes
        String expectedPluginId = "com.fartech.eclipse.aiagent";
        String expectedChatViewId = "com.fartech.eclipse.aiagent.ui.views.ChatView";
        String expectedProgressViewId = "com.fartech.eclipse.aiagent.ui.views.ProgressView";
        String expectedDiffViewId = "com.fartech.eclipse.aiagent.ui.views.DiffApprovalView";
        
        // Verify the constants are reasonable
        assertTrue(expectedPluginId.contains("aiagent"));
        assertTrue(expectedChatViewId.contains("ChatView"));
        assertTrue(expectedProgressViewId.contains("ProgressView"));
        assertTrue(expectedDiffViewId.contains("DiffApprovalView"));
    }
    
    @Test
    public void testDependencyCompatibility() {
        // Test that our dependencies are compatible
        
        // Test Gson
        Gson testGson = new Gson();
        String json = testGson.toJson("test");
        assertEquals("\"test\"", json);
        
        // Test that we can create HTTP components (would need actual HTTP client in real test)
        // For now, just verify the classes exist
        try {
            Class.forName("org.apache.http.client.methods.HttpPost");
            Class.forName("org.apache.http.impl.client.HttpClients");
            Class.forName("com.google.gson.Gson");
        } catch (ClassNotFoundException e) {
            fail("Required dependency class not found: " + e.getMessage());
        }
    }
}
