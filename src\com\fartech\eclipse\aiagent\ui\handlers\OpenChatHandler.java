package com.fartech.eclipse.aiagent.ui.handlers;

import org.eclipse.core.commands.AbstractHandler;
import org.eclipse.core.commands.ExecutionEvent;
import org.eclipse.core.commands.ExecutionException;
import org.eclipse.ui.IWorkbenchPage;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;
import org.eclipse.ui.handlers.HandlerUtil;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.ui.views.ChatView;

/**
 * <PERSON><PERSON> for opening the AI Agent chat view.
 */
public class OpenChatHandler extends AbstractHandler {
    
    private AIAgentLogger logger;
    
    public OpenChatHandler() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public Object execute(ExecutionEvent event) throws ExecutionException {
        try {
            IWorkbenchWindow window = HandlerUtil.getActiveWorkbenchWindowChecked(event);
            IWorkbenchPage page = window.getActivePage();
            
            if (page != null) {
                page.showView(ChatView.ID);
                logger.info("Opened AI Agent chat view");
            }
            
        } catch (PartInitException e) {
            logger.error("Failed to open chat view", e);
            throw new ExecutionException("Failed to open AI Agent chat view", e);
        }
        
        return null;
    }
}
