package com.fartech.eclipse.aiagent.core.planning;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents an execution plan with multiple steps.
 */
public class ExecutionPlan {
    
    private final String originalGoal;
    private final List<ExecutionStep> steps;
    private int currentStepIndex;
    private long createdTimestamp;
    
    public ExecutionPlan(String originalGoal, List<ExecutionStep> steps) {
        this.originalGoal = originalGoal;
        this.steps = steps != null ? new ArrayList<ExecutionStep>(steps) : new ArrayList<ExecutionStep>();
        this.currentStepIndex = 0;
        this.createdTimestamp = System.currentTimeMillis();
    }
    
    /**
     * Get the original user goal
     */
    public String getOriginalGoal() {
        return originalGoal;
    }
    
    /**
     * Get all execution steps
     */
    public List<ExecutionStep> getSteps() {
        return new ArrayList<ExecutionStep>(steps);
    }
    
    /**
     * Get the current step index
     */
    public int getCurrentStepIndex() {
        return currentStepIndex;
    }
    
    /**
     * Get the current step
     */
    public ExecutionStep getCurrentStep() {
        if (currentStepIndex >= 0 && currentStepIndex < steps.size()) {
            return steps.get(currentStepIndex);
        }
        return null;
    }
    
    /**
     * Move to the next step
     */
    public boolean moveToNextStep() {
        if (currentStepIndex < steps.size() - 1) {
            currentStepIndex++;
            return true;
        }
        return false;
    }
    
    /**
     * Move to a specific step
     */
    public boolean moveToStep(int stepIndex) {
        if (stepIndex >= 0 && stepIndex < steps.size()) {
            currentStepIndex = stepIndex;
            return true;
        }
        return false;
    }
    
    /**
     * Check if there are more steps to execute
     */
    public boolean hasMoreSteps() {
        return currentStepIndex < steps.size();
    }
    
    /**
     * Check if the plan is complete
     */
    public boolean isComplete() {
        return currentStepIndex >= steps.size();
    }
    
    /**
     * Get the total number of steps
     */
    public int getTotalSteps() {
        return steps.size();
    }
    
    /**
     * Get the progress percentage (0-100)
     */
    public int getProgressPercentage() {
        if (steps.isEmpty()) {
            return 100;
        }
        return (currentStepIndex * 100) / steps.size();
    }
    
    /**
     * Get the creation timestamp
     */
    public long getCreatedTimestamp() {
        return createdTimestamp;
    }
    
    /**
     * Add a step to the plan
     */
    public void addStep(ExecutionStep step) {
        if (step != null) {
            steps.add(step);
        }
    }
    
    /**
     * Insert a step at a specific position
     */
    public void insertStep(int index, ExecutionStep step) {
        if (step != null && index >= 0 && index <= steps.size()) {
            steps.add(index, step);
            
            // Adjust current step index if necessary
            if (index <= currentStepIndex) {
                currentStepIndex++;
            }
        }
    }
    
    /**
     * Remove a step from the plan
     */
    public boolean removeStep(int index) {
        if (index >= 0 && index < steps.size()) {
            steps.remove(index);
            
            // Adjust current step index if necessary
            if (index < currentStepIndex) {
                currentStepIndex--;
            } else if (index == currentStepIndex && currentStepIndex >= steps.size()) {
                currentStepIndex = steps.size() - 1;
            }
            
            return true;
        }
        return false;
    }
    
    /**
     * Get a summary of the plan
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Execution Plan for: ").append(originalGoal).append("\n");
        summary.append("Total Steps: ").append(steps.size()).append("\n");
        summary.append("Current Step: ").append(currentStepIndex + 1).append("/").append(steps.size()).append("\n");
        summary.append("Progress: ").append(getProgressPercentage()).append("%\n");
        
        if (!steps.isEmpty()) {
            summary.append("\nSteps:\n");
            for (int i = 0; i < steps.size(); i++) {
                ExecutionStep step = steps.get(i);
                String status = "";
                if (i < currentStepIndex) {
                    status = " [COMPLETED]";
                } else if (i == currentStepIndex) {
                    status = " [CURRENT]";
                } else {
                    status = " [PENDING]";
                }
                
                summary.append(String.format("%d. %s: %s%s\n", 
                    step.getStepNumber(), 
                    step.getActionType(), 
                    step.getOperation(),
                    status));
            }
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return "ExecutionPlan{" +
                "originalGoal='" + originalGoal + '\'' +
                ", totalSteps=" + steps.size() +
                ", currentStep=" + (currentStepIndex + 1) +
                ", progress=" + getProgressPercentage() + "%" +
                '}';
    }
}
