package com.fartech.eclipse.aiagent.core.context;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.adapters.EditorAdapter;
import com.fartech.eclipse.aiagent.adapters.FileSystemAdapter;
import com.fartech.eclipse.aiagent.adapters.TerminalAdapter;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Manages the context for AI agent operations.
 * Collects and maintains information about the current development environment.
 */
public class ContextManager {
    
    private final AIAgentLogger logger;
    private final FileSystemAdapter fileSystemAdapter;
    private final EditorAdapter editorAdapter;
    private final TerminalAdapter terminalAdapter;
    
    private final Map<String, Object> contextData;
    private final List<String> conversationHistory;
    
    private String currentUserInput;
    private String currentWorkingDirectory;
    private boolean initialized = false;
    
    public ContextManager() {
        this.logger = Activator.getDefault().getLogger();
        this.fileSystemAdapter = new FileSystemAdapter();
        this.editorAdapter = new EditorAdapter();
        this.terminalAdapter = new TerminalAdapter();
        this.contextData = new ConcurrentHashMap<String, Object>();
        this.conversationHistory = new ArrayList<String>();
    }
    
    /**
     * Initialize the context manager and all adapters
     */
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("ContextManager already initialized");
            return;
        }
        
        try {
            logger.info("Initializing ContextManager...");
            
            // Initialize adapters
            fileSystemAdapter.initialize();
            editorAdapter.initialize();
            terminalAdapter.initialize();
            
            // Initialize context data
            refreshContext();
            
            initialized = true;
            logger.info("ContextManager initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize ContextManager", e);
            throw e;
        }
    }
    
    /**
     * Refresh the context with current environment information
     */
    public void refreshContext() {
        if (!initialized) {
            return;
        }
        
        try {
            logger.debug("Refreshing context...");
            
            // Clear existing context
            contextData.clear();
            
            // Collect workspace information
            collectWorkspaceContext();
            
            // Collect editor information
            collectEditorContext();
            
            // Collect project information
            collectProjectContext();
            
            logger.debug("Context refreshed successfully");
            
        } catch (Exception e) {
            logger.warn("Error refreshing context", e);
        }
    }
    
    private void collectWorkspaceContext() {
        try {
            List<String> projects = fileSystemAdapter.getProjects();
            contextData.put("workspace.projects", projects);
            contextData.put("workspace.projectCount", projects.size());
            
            // Set current working directory to first project if available
            if (!projects.isEmpty() && currentWorkingDirectory == null) {
                currentWorkingDirectory = "/" + projects.get(0);
            }
            
        } catch (Exception e) {
            logger.warn("Error collecting workspace context", e);
        }
    }
    
    private void collectEditorContext() {
        try {
            String activeFilePath = editorAdapter.getActiveEditorFilePath();
            if (activeFilePath != null) {
                contextData.put("editor.activeFile", activeFilePath);
                
                String selectedText = editorAdapter.getSelectedText();
                if (selectedText != null && !selectedText.trim().isEmpty()) {
                    contextData.put("editor.selectedText", selectedText);
                }
                
                // Get file content if it's not too large
                String content = editorAdapter.getActiveEditorContent();
                if (content != null && content.length() < 50000) { // Limit to 50KB
                    contextData.put("editor.activeFileContent", content);
                }
            }
            
        } catch (Exception e) {
            logger.warn("Error collecting editor context", e);
        }
    }
    
    private void collectProjectContext() {
        try {
            if (currentWorkingDirectory != null) {
                // Check for common project files
                Map<String, Boolean> projectFiles = new HashMap<String, Boolean>();
                projectFiles.put("pom.xml", fileSystemAdapter.fileExists(currentWorkingDirectory + "/pom.xml"));
                projectFiles.put("build.gradle", fileSystemAdapter.fileExists(currentWorkingDirectory + "/build.gradle"));
                projectFiles.put("package.json", fileSystemAdapter.fileExists(currentWorkingDirectory + "/package.json"));
                projectFiles.put(".project", fileSystemAdapter.fileExists(currentWorkingDirectory + "/.project"));
                
                contextData.put("project.files", projectFiles);
                
                // Determine project type
                String projectType = "unknown";
                if (projectFiles.get("pom.xml")) {
                    projectType = "maven";
                } else if (projectFiles.get("build.gradle")) {
                    projectType = "gradle";
                } else if (projectFiles.get("package.json")) {
                    projectType = "nodejs";
                }
                
                contextData.put("project.type", projectType);
            }
            
        } catch (Exception e) {
            logger.warn("Error collecting project context", e);
        }
    }
    
    /**
     * Set the current user input
     */
    public void setUserInput(String userInput) {
        this.currentUserInput = userInput;
        if (userInput != null && !userInput.trim().isEmpty()) {
            conversationHistory.add("User: " + userInput);
        }
    }
    
    /**
     * Add an AI response to the conversation history
     */
    public void addAIResponse(String response) {
        if (response != null && !response.trim().isEmpty()) {
            conversationHistory.add("AI: " + response);
        }
    }
    
    /**
     * Get the current user input
     */
    public String getCurrentUserInput() {
        return currentUserInput;
    }
    
    /**
     * Get the conversation history
     */
    public List<String> getConversationHistory() {
        return new ArrayList<String>(conversationHistory);
    }
    
    /**
     * Get the recent conversation history (last N entries)
     */
    public List<String> getRecentConversationHistory(int maxEntries) {
        List<String> history = getConversationHistory();
        int startIndex = Math.max(0, history.size() - maxEntries);
        return history.subList(startIndex, history.size());
    }
    
    /**
     * Set a context value
     */
    public void setContextValue(String key, Object value) {
        contextData.put(key, value);
    }
    
    /**
     * Get a context value
     */
    public Object getContextValue(String key) {
        return contextData.get(key);
    }
    
    /**
     * Get a context value with a default
     */
    @SuppressWarnings("unchecked")
    public <T> T getContextValue(String key, T defaultValue) {
        Object value = contextData.get(key);
        if (value != null) {
            try {
                return (T) value;
            } catch (ClassCastException e) {
                logger.warn("Context value type mismatch for key: " + key, e);
            }
        }
        return defaultValue;
    }
    
    /**
     * Get all context data
     */
    public Map<String, Object> getAllContextData() {
        return new HashMap<String, Object>(contextData);
    }
    
    /**
     * Build a context summary for AI prompts
     */
    public String buildContextSummary() {
        StringBuilder summary = new StringBuilder();
        
        summary.append("=== Development Context ===\n");
        
        // Workspace info
        List<String> projects = getContextValue("workspace.projects", new ArrayList<String>());
        summary.append("Workspace Projects: ").append(projects).append("\n");
        
        // Active file info
        String activeFile = getContextValue("editor.activeFile", (String) null);
        if (activeFile != null) {
            summary.append("Active File: ").append(activeFile).append("\n");
            
            String selectedText = getContextValue("editor.selectedText", (String) null);
            if (selectedText != null && !selectedText.trim().isEmpty()) {
                summary.append("Selected Text: ").append(selectedText.substring(0, Math.min(200, selectedText.length()))).append("\n");
            }
        }
        
        // Project type
        String projectType = getContextValue("project.type", "unknown");
        summary.append("Project Type: ").append(projectType).append("\n");
        
        // Recent conversation
        List<String> recentHistory = getRecentConversationHistory(5);
        if (!recentHistory.isEmpty()) {
            summary.append("Recent Conversation:\n");
            for (String entry : recentHistory) {
                summary.append("  ").append(entry).append("\n");
            }
        }
        
        return summary.toString();
    }
    
    /**
     * Get the file system adapter
     */
    public FileSystemAdapter getFileSystemAdapter() {
        return fileSystemAdapter;
    }
    
    /**
     * Get the editor adapter
     */
    public EditorAdapter getEditorAdapter() {
        return editorAdapter;
    }
    
    /**
     * Get the terminal adapter
     */
    public TerminalAdapter getTerminalAdapter() {
        return terminalAdapter;
    }
    
    /**
     * Set the current working directory
     */
    public void setCurrentWorkingDirectory(String directory) {
        this.currentWorkingDirectory = directory;
        setContextValue("project.workingDirectory", directory);
    }
    
    /**
     * Get the current working directory
     */
    public String getCurrentWorkingDirectory() {
        return currentWorkingDirectory;
    }
    
    /**
     * Shutdown the context manager
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        logger.info("Shutting down ContextManager...");
        
        try {
            terminalAdapter.shutdown();
            editorAdapter.shutdown();
            fileSystemAdapter.shutdown();
            
            contextData.clear();
            conversationHistory.clear();
            
            initialized = false;
            logger.info("ContextManager shut down successfully");
            
        } catch (Exception e) {
            logger.error("Error during ContextManager shutdown", e);
        }
    }
}
