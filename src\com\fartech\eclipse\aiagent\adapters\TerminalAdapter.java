package com.fartech.eclipse.aiagent.adapters;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Adapter for executing terminal/shell commands.
 * Provides safe execution of system commands with output capture.
 */
public class TerminalAdapter implements IDEAdapter {
    
    private final AIAgentLogger logger;
    private boolean initialized = false;
    
    public TerminalAdapter() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("TerminalAdapter already initialized");
            return;
        }
        
        try {
            // Test basic command execution capability
            String os = System.getProperty("os.name").toLowerCase();
            String testCommand = os.contains("win") ? "echo test" : "echo test";
            
            Process process = Runtime.getRuntime().exec(testCommand);
            int exitCode = process.waitFor();
            
            if (exitCode != 0) {
                throw new Exception("Failed to execute test command");
            }
            
            initialized = true;
            logger.info("TerminalAdapter initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize TerminalAdapter", e);
            throw e;
        }
    }
    
    @Override
    public boolean isReady() {
        return initialized;
    }
    
    @Override
    public void shutdown() {
        initialized = false;
        logger.info("TerminalAdapter shut down");
    }
    
    @Override
    public String getAdapterType() {
        return "terminal";
    }
    
    @Override
    public String getDescription() {
        return "System command execution";
    }
    
    /**
     * Execute a command and return the result
     * @param command the command to execute
     * @return the command execution result
     */
    public CommandResult executeCommand(String command) {
        return executeCommand(command, null, 30000); // 30 second default timeout
    }
    
    /**
     * Execute a command with a working directory and timeout
     * @param command the command to execute
     * @param workingDirectory the working directory (null for default)
     * @param timeoutMs the timeout in milliseconds
     * @return the command execution result
     */
    public CommandResult executeCommand(String command, String workingDirectory, long timeoutMs) {
        if (!isReady()) {
            return CommandResult.error("TerminalAdapter not initialized");
        }
        
        if (command == null || command.trim().isEmpty()) {
            return CommandResult.error("Command cannot be null or empty");
        }
        
        logger.debug("Executing command: " + command);
        
        try {
            ProcessBuilder processBuilder = new ProcessBuilder();
            
            // Set up command based on OS
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                processBuilder.command("cmd", "/c", command);
            } else {
                processBuilder.command("sh", "-c", command);
            }
            
            // Set working directory if specified
            if (workingDirectory != null && !workingDirectory.trim().isEmpty()) {
                processBuilder.directory(new java.io.File(workingDirectory));
            }
            
            // Redirect error stream to output stream
            processBuilder.redirectErrorStream(true);
            
            long startTime = System.currentTimeMillis();
            Process process = processBuilder.start();
            
            // Read output
            StringBuilder output = new StringBuilder();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            
            // Wait for process to complete with timeout
            boolean finished = process.waitFor(timeoutMs, TimeUnit.MILLISECONDS);
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (!finished) {
                process.destroyForcibly();
                logger.warn("Command timed out: " + command);
                return CommandResult.timeout("Command timed out after " + timeoutMs + "ms", output.toString());
            }
            
            int exitCode = process.exitValue();
            String outputStr = output.toString();
            
            logger.debug("Command completed with exit code " + exitCode + " in " + executionTime + "ms");
            
            if (exitCode == 0) {
                return CommandResult.success(outputStr, exitCode, executionTime);
            } else {
                return CommandResult.error("Command failed with exit code " + exitCode, outputStr, exitCode, executionTime);
            }
            
        } catch (IOException e) {
            logger.error("IO error executing command: " + command, e);
            return CommandResult.error("IO error: " + e.getMessage());
        } catch (InterruptedException e) {
            logger.error("Command interrupted: " + command, e);
            Thread.currentThread().interrupt();
            return CommandResult.error("Command interrupted: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error executing command: " + command, e);
            return CommandResult.error("Unexpected error: " + e.getMessage());
        }
    }
    
    /**
     * Execute a Maven command in the specified project directory
     * @param projectPath the path to the project directory
     * @param mavenGoals the Maven goals to execute (e.g., "clean compile")
     * @return the command execution result
     */
    public CommandResult executeMavenCommand(String projectPath, String mavenGoals) {
        String command = "mvn " + mavenGoals;
        return executeCommand(command, projectPath, 120000); // 2 minute timeout for Maven
    }
    
    /**
     * Execute a Gradle command in the specified project directory
     * @param projectPath the path to the project directory
     * @param gradleTasks the Gradle tasks to execute (e.g., "clean build")
     * @return the command execution result
     */
    public CommandResult executeGradleCommand(String projectPath, String gradleTasks) {
        String command = "gradle " + gradleTasks;
        return executeCommand(command, projectPath, 120000); // 2 minute timeout for Gradle
    }
    
    /**
     * Execute a Git command in the specified directory
     * @param projectPath the path to the project directory
     * @param gitCommand the Git command to execute (e.g., "status")
     * @return the command execution result
     */
    public CommandResult executeGitCommand(String projectPath, String gitCommand) {
        String command = "git " + gitCommand;
        return executeCommand(command, projectPath, 30000); // 30 second timeout for Git
    }
    
    /**
     * Result of a command execution
     */
    public static class CommandResult {
        private final boolean success;
        private final String output;
        private final String errorMessage;
        private final int exitCode;
        private final long executionTimeMs;
        private final boolean timedOut;
        
        private CommandResult(boolean success, String output, String errorMessage, int exitCode, long executionTimeMs, boolean timedOut) {
            this.success = success;
            this.output = output != null ? output : "";
            this.errorMessage = errorMessage;
            this.exitCode = exitCode;
            this.executionTimeMs = executionTimeMs;
            this.timedOut = timedOut;
        }
        
        public static CommandResult success(String output, int exitCode, long executionTimeMs) {
            return new CommandResult(true, output, null, exitCode, executionTimeMs, false);
        }
        
        public static CommandResult error(String errorMessage) {
            return new CommandResult(false, "", errorMessage, -1, 0, false);
        }
        
        public static CommandResult error(String errorMessage, String output, int exitCode, long executionTimeMs) {
            return new CommandResult(false, output, errorMessage, exitCode, executionTimeMs, false);
        }
        
        public static CommandResult timeout(String errorMessage, String output) {
            return new CommandResult(false, output, errorMessage, -1, 0, true);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getOutput() {
            return output;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public int getExitCode() {
            return exitCode;
        }
        
        public long getExecutionTimeMs() {
            return executionTimeMs;
        }
        
        public boolean isTimedOut() {
            return timedOut;
        }
        
        @Override
        public String toString() {
            return "CommandResult{" +
                    "success=" + success +
                    ", exitCode=" + exitCode +
                    ", executionTimeMs=" + executionTimeMs +
                    ", timedOut=" + timedOut +
                    ", output='" + (output.length() > 100 ? output.substring(0, 100) + "..." : output) + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    '}';
        }
    }
}
