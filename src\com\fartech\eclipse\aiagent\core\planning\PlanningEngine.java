package com.fartech.eclipse.aiagent.core.planning;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.context.ContextManager;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.providers.AIProviderClient;
import com.fartech.eclipse.aiagent.providers.AIProviderException;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;

/**
 * Planning and reasoning engine for the AI Agent.
 * Responsible for breaking down complex user goals into executable steps.
 */
public class PlanningEngine {
    
    private final AIAgentLogger logger;
    private final ContextManager contextManager;
    private final AIProviderClient aiProviderClient;
    
    private ExecutionPlan currentPlan;
    private final AtomicBoolean planning = new AtomicBoolean(false);
    private boolean initialized = false;
    
    public PlanningEngine(ContextManager contextManager, AIProviderClient aiProviderClient) {
        this.logger = Activator.getDefault().getLogger();
        this.contextManager = contextManager;
        this.aiProviderClient = aiProviderClient;
    }
    
    /**
     * Initialize the planning engine
     */
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("PlanningEngine already initialized");
            return;
        }
        
        try {
            logger.info("Initializing PlanningEngine...");
            
            // Verify dependencies
            if (contextManager == null) {
                throw new Exception("ContextManager is required");
            }
            
            if (aiProviderClient == null) {
                throw new Exception("AIProviderClient is required");
            }
            
            initialized = true;
            logger.info("PlanningEngine initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize PlanningEngine", e);
            throw e;
        }
    }
    
    /**
     * Generate an execution plan for the given user input
     * @param userInput the user's natural language goal
     * @throws Exception if planning fails
     */
    public void generatePlan(String userInput) throws Exception {
        if (!initialized) {
            throw new IllegalStateException("PlanningEngine not initialized");
        }
        
        if (userInput == null || userInput.trim().isEmpty()) {
            throw new IllegalArgumentException("User input cannot be null or empty");
        }
        
        if (planning.getAndSet(true)) {
            throw new IllegalStateException("Planning already in progress");
        }
        
        try {
            logger.info("Generating execution plan for: " + userInput);
            
            // Refresh context before planning
            contextManager.refreshContext();
            
            // Build planning prompt
            String planningPrompt = buildPlanningPrompt(userInput);
            
            // Create AI request
            AIRequest request = new AIRequest(planningPrompt);
            request.setSystemMessage(getPlanningSystemMessage());
            request.setMaxTokens(2000);
            request.setTemperature(0.3); // Lower temperature for more focused planning
            
            // Get plan from AI
            AIResponse response = aiProviderClient.sendRequest(request);
            
            if (!response.isSuccess()) {
                throw new Exception("AI planning failed: " + response.getErrorMessage());
            }
            
            // Parse the response into an execution plan
            currentPlan = parsePlanFromResponse(response.getContent(), userInput);
            
            logger.info("Generated execution plan with " + currentPlan.getSteps().size() + " steps");
            
        } catch (AIProviderException e) {
            logger.error("AI provider error during planning", e);
            throw new Exception("Planning failed due to AI provider error: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Error during plan generation", e);
            throw e;
        } finally {
            planning.set(false);
        }
    }
    
    private String buildPlanningPrompt(String userInput) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are an AI assistant helping with software development in Eclipse IDE.\n\n");
        prompt.append("Current Context:\n");
        prompt.append(contextManager.buildContextSummary());
        prompt.append("\n");
        
        prompt.append("User Goal: ").append(userInput).append("\n\n");
        
        prompt.append("Please create a detailed execution plan to achieve this goal. ");
        prompt.append("Break it down into specific, actionable steps that can be executed by IDE adapters.\n\n");
        
        prompt.append("Available Tools:\n");
        prompt.append("- FileSystem: read, write, create, delete files and directories\n");
        prompt.append("- Editor: open files, get/set content, get/set selections\n");
        prompt.append("- Terminal: execute commands (Maven, Gradle, Git, etc.)\n");
        prompt.append("- Build: compile, test, package projects\n\n");
        
        prompt.append("Format your response as a numbered list of steps, where each step includes:\n");
        prompt.append("1. Action type (FileSystem, Editor, Terminal, etc.)\n");
        prompt.append("2. Specific operation to perform\n");
        prompt.append("3. Expected outcome\n\n");
        
        prompt.append("Example format:\n");
        prompt.append("1. FileSystem: Read file 'src/main/java/Example.java' to understand current implementation\n");
        prompt.append("2. Editor: Open the file in editor for modification\n");
        prompt.append("3. Terminal: Run 'mvn test' to verify current test status\n\n");
        
        prompt.append("Your plan:");
        
        return prompt.toString();
    }
    
    private String getPlanningSystemMessage() {
        return "You are an expert software development assistant. " +
               "Create precise, executable plans that break down complex development tasks into specific steps. " +
               "Focus on safety, requiring user approval for destructive operations. " +
               "Consider the current project context and available tools when planning.";
    }
    
    private ExecutionPlan parsePlanFromResponse(String response, String originalGoal) {
        List<ExecutionStep> steps = new ArrayList<ExecutionStep>();
        
        try {
            String[] lines = response.split("\n");
            int stepNumber = 1;
            
            for (String line : lines) {
                line = line.trim();
                
                // Skip empty lines and headers
                if (line.isEmpty() || line.toLowerCase().contains("plan") || line.toLowerCase().contains("step")) {
                    continue;
                }
                
                // Look for numbered steps
                if (line.matches("^\\d+\\..*")) {
                    ExecutionStep step = parseStepFromLine(line, stepNumber);
                    if (step != null) {
                        steps.add(step);
                        stepNumber++;
                    }
                }
            }
            
            // If no numbered steps found, try to parse the entire response as a single step
            if (steps.isEmpty()) {
                ExecutionStep step = new ExecutionStep(
                    1,
                    "General",
                    "Execute user request",
                    response,
                    "Complete user goal"
                );
                steps.add(step);
            }
            
        } catch (Exception e) {
            logger.warn("Error parsing plan from AI response, creating fallback plan", e);
            
            // Create a fallback plan
            ExecutionStep fallbackStep = new ExecutionStep(
                1,
                "General",
                "Execute user request",
                "Attempt to fulfill user request: " + originalGoal,
                "Complete user goal"
            );
            steps.add(fallbackStep);
        }
        
        return new ExecutionPlan(originalGoal, steps);
    }
    
    private ExecutionStep parseStepFromLine(String line, int stepNumber) {
        try {
            // Remove step number prefix
            String content = line.replaceFirst("^\\d+\\.\\s*", "");
            
            // Try to extract action type and description
            String actionType = "General";
            String operation = content;
            String expectedOutcome = "Step completed";
            
            // Look for action type pattern (ActionType: description)
            if (content.contains(":")) {
                String[] parts = content.split(":", 2);
                if (parts.length == 2) {
                    actionType = parts[0].trim();
                    operation = parts[1].trim();
                }
            }
            
            return new ExecutionStep(stepNumber, actionType, operation, operation, expectedOutcome);
            
        } catch (Exception e) {
            logger.warn("Error parsing step from line: " + line, e);
            return null;
        }
    }
    
    /**
     * Get the current execution plan
     */
    public ExecutionPlan getCurrentPlan() {
        return currentPlan;
    }
    
    /**
     * Check if planning is currently in progress
     */
    public boolean isPlanning() {
        return planning.get();
    }
    
    /**
     * Cancel current planning operation
     */
    public void cancelCurrentPlanning() {
        if (planning.get()) {
            logger.info("Cancelling current planning operation");
            planning.set(false);
        }
    }
    
    /**
     * Re-plan based on feedback from execution
     * @param feedback the feedback from execution
     * @param originalGoal the original user goal
     * @throws Exception if re-planning fails
     */
    public void replan(String feedback, String originalGoal) throws Exception {
        logger.info("Re-planning based on feedback: " + feedback);
        
        String replanPrompt = buildReplanPrompt(feedback, originalGoal);
        
        AIRequest request = new AIRequest(replanPrompt);
        request.setSystemMessage(getPlanningSystemMessage());
        request.setMaxTokens(2000);
        request.setTemperature(0.3);
        
        AIResponse response = aiProviderClient.sendRequest(request);
        
        if (!response.isSuccess()) {
            throw new Exception("AI re-planning failed: " + response.getErrorMessage());
        }
        
        currentPlan = parsePlanFromResponse(response.getContent(), originalGoal);
        logger.info("Re-planned with " + currentPlan.getSteps().size() + " steps");
    }
    
    private String buildReplanPrompt(String feedback, String originalGoal) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("The previous execution plan encountered an issue. Please create a new plan.\n\n");
        prompt.append("Original Goal: ").append(originalGoal).append("\n");
        prompt.append("Execution Feedback: ").append(feedback).append("\n\n");
        prompt.append("Current Context:\n");
        prompt.append(contextManager.buildContextSummary());
        prompt.append("\n\n");
        prompt.append("Please create a revised execution plan that addresses the feedback and achieves the original goal:");
        
        return prompt.toString();
    }
    
    /**
     * Shutdown the planning engine
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        logger.info("Shutting down PlanningEngine...");
        
        cancelCurrentPlanning();
        currentPlan = null;
        initialized = false;
        
        logger.info("PlanningEngine shut down successfully");
    }
}
