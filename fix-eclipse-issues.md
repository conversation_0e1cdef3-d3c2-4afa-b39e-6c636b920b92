# Fix Eclipse Issues - Quick Guide

This guide will help you resolve the Eclipse build path and plugin issues.

## Issues Fixed

✅ **Package 'com.fartech.eclipse.aiagent.api' does not exist** - Created API package
✅ **Missing 'Automatic-Module-Name' header** - Added to MANIFEST.MF
✅ **Missing source build entries** - Added to build.properties

## Steps to Resolve Remaining Issues

### 1. Download Dependencies

Run one of these commands to download the required JAR files:

**Windows:**
```cmd
download-dependencies.bat
```

**Linux/macOS:**
```bash
chmod +x download-dependencies.sh
./download-dependencies.sh
```

**Manual Download (if scripts don't work):**
1. Create `lib/` directory in your project root
2. Download these files to the `lib/` directory:
   - https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar
   - https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar
   - https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar
   - https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar

### 2. Refresh Eclipse Project

After downloading dependencies:
1. Right-click on your project in Eclipse
2. Select **Refresh** (or press F5)
3. Wait for Eclipse to rebuild the project

### 3. Verify Build Path

If issues persist:
1. Right-click project → **Properties**
2. Go to **Java Build Path** → **Libraries**
3. Verify that all JAR files from `lib/` are listed
4. If missing, click **Add JARs** and add them manually

### 4. Clean and Rebuild

1. Go to **Project** → **Clean**
2. Select your project
3. Click **Clean**
4. Eclipse will automatically rebuild

## Expected Result

After following these steps, you should have:

✅ No build path errors
✅ No plugin manifest errors
✅ All dependencies resolved
✅ Project builds successfully

## File Structure

Your project should look like this:

```
com.fartech.eclipse.aiagent/
├── META-INF/
│   └── MANIFEST.MF          ✅ Fixed
├── build.properties         ✅ Fixed
├── plugin.xml
├── src/
│   └── com/fartech/eclipse/aiagent/
│       ├── api/             ✅ Created
│       │   ├── package-info.java
│       │   └── IAIAgentService.java
│       ├── Activator.java
│       ├── core/
│       ├── providers/
│       ├── adapters/
│       └── ui/
├── lib/                     ⚠️ Download needed
│   ├── httpclient-4.5.14.jar
│   ├── httpcore-4.4.16.jar
│   ├── gson-2.10.1.jar
│   └── commons-logging-1.2.jar
└── test/
```

## Troubleshooting

### If download scripts fail:
- Check internet connection
- Install curl if not available
- Download JAR files manually from Maven Central

### If Eclipse still shows errors:
1. **Project** → **Clean** → **Clean all projects**
2. Restart Eclipse
3. Check that all JAR files are in the `lib/` directory
4. Verify file sizes (should be > 0 bytes)

### If build path issues persist:
1. Right-click project → **Properties**
2. **Java Build Path** → **Libraries**
3. Remove any missing entries
4. **Add JARs** → Select all JARs from `lib/` folder

## Next Steps

Once all issues are resolved:
1. The project should compile without errors
2. You can run the plugin by right-clicking the project
3. Select **Run As** → **Eclipse Application**
4. A new Eclipse instance will start with your plugin loaded

## Support

If you continue to have issues:
1. Check the Eclipse Error Log (**Window** → **Show View** → **Error Log**)
2. Verify Java 8 compatibility
3. Ensure Eclipse 2019-12 or compatible version
4. Check that all required Eclipse plugins are installed
