#!/bin/bash
# Download dependencies for Eclipse AI Agent Plugin
# This script downloads the required JAR files from Maven Central

echo "========================================"
echo "Downloading Eclipse AI Agent Dependencies"
echo "========================================"

# Create lib directory if it doesn't exist
mkdir -p lib

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo "ERROR: curl is not installed or not in PATH"
    echo "Please install curl or download the JAR files manually"
    echo "See lib/README.md for download URLs"
    exit 1
fi

echo "Downloading dependencies from Maven Central..."
echo ""

# Download Apache HttpClient 4.5.14
echo "[1/4] Downloading httpclient-4.5.14.jar..."
curl -L -o "lib/httpclient-4.5.14.jar" "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to download httpclient-4.5.14.jar"
    exit 1
fi

# Download Apache HttpCore 4.4.16
echo "[2/4] Downloading httpcore-4.4.16.jar..."
curl -L -o "lib/httpcore-4.4.16.jar" "https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to download httpcore-4.4.16.jar"
    exit 1
fi

# Download Gson 2.10.1
echo "[3/4] Downloading gson-2.10.1.jar..."
curl -L -o "lib/gson-2.10.1.jar" "https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to download gson-2.10.1.jar"
    exit 1
fi

# Download Commons Logging 1.2
echo "[4/4] Downloading commons-logging-1.2.jar..."
curl -L -o "lib/commons-logging-1.2.jar" "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to download commons-logging-1.2.jar"
    exit 1
fi

echo ""
echo "========================================"
echo "Dependencies downloaded successfully!"
echo "========================================"
echo ""
echo "Downloaded files:"
ls -la lib/*.jar
echo ""
echo "You can now refresh your Eclipse project to resolve build path errors."
echo "Go to Project > Refresh or press F5 in Eclipse."
echo "========================================"
