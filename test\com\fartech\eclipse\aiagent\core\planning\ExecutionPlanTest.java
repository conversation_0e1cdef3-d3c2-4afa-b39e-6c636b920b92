package com.fartech.eclipse.aiagent.core.planning;

import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;

import com.fartech.eclipse.aiagent.core.planning.ExecutionStep.ExecutionStatus;

/**
 * Unit tests for ExecutionPlan.
 */
public class ExecutionPlanTest {
    
    private ExecutionPlan executionPlan;
    private List<ExecutionStep> steps;
    
    @Before
    public void setUp() {
        steps = new ArrayList<ExecutionStep>();
        steps.add(new ExecutionStep(1, "FileSystem", "Read file", "Read the source file", "File content retrieved"));
        steps.add(new ExecutionStep(2, "Editor", "Open file", "Open file in editor", "File opened"));
        steps.add(new ExecutionStep(3, "Terminal", "Run tests", "Execute unit tests", "Tests completed"));
        
        executionPlan = new ExecutionPlan("Create a new feature", steps);
    }
    
    @Test
    public void testConstructor() {
        assertEquals("Create a new feature", executionPlan.getOriginalGoal());
        assertEquals(3, executionPlan.getTotalSteps());
        assertEquals(0, executionPlan.getCurrentStepIndex());
        assertFalse(executionPlan.isComplete());
        assertTrue(executionPlan.hasMoreSteps());
    }
    
    @Test
    public void testConstructorWithNullSteps() {
        ExecutionPlan plan = new ExecutionPlan("Test goal", null);
        assertEquals("Test goal", plan.getOriginalGoal());
        assertEquals(0, plan.getTotalSteps());
        assertTrue(plan.isComplete());
        assertFalse(plan.hasMoreSteps());
    }
    
    @Test
    public void testGetCurrentStep() {
        ExecutionStep currentStep = executionPlan.getCurrentStep();
        assertNotNull(currentStep);
        assertEquals(1, currentStep.getStepNumber());
        assertEquals("FileSystem", currentStep.getActionType());
    }
    
    @Test
    public void testMoveToNextStep() {
        assertTrue(executionPlan.moveToNextStep());
        assertEquals(1, executionPlan.getCurrentStepIndex());
        
        ExecutionStep currentStep = executionPlan.getCurrentStep();
        assertEquals(2, currentStep.getStepNumber());
        
        assertTrue(executionPlan.moveToNextStep());
        assertEquals(2, executionPlan.getCurrentStepIndex());
        
        // Should return false when trying to move beyond last step
        assertFalse(executionPlan.moveToNextStep());
        assertEquals(2, executionPlan.getCurrentStepIndex());
    }
    
    @Test
    public void testMoveToStep() {
        assertTrue(executionPlan.moveToStep(2));
        assertEquals(2, executionPlan.getCurrentStepIndex());
        
        assertTrue(executionPlan.moveToStep(0));
        assertEquals(0, executionPlan.getCurrentStepIndex());
        
        // Invalid step indices
        assertFalse(executionPlan.moveToStep(-1));
        assertFalse(executionPlan.moveToStep(10));
        assertEquals(0, executionPlan.getCurrentStepIndex()); // Should remain unchanged
    }
    
    @Test
    public void testHasMoreSteps() {
        assertTrue(executionPlan.hasMoreSteps());
        
        executionPlan.moveToStep(2); // Last step
        assertTrue(executionPlan.hasMoreSteps());
        
        executionPlan.moveToStep(3); // Beyond last step
        assertFalse(executionPlan.hasMoreSteps());
    }
    
    @Test
    public void testIsComplete() {
        assertFalse(executionPlan.isComplete());
        
        executionPlan.moveToStep(2); // Last step
        assertFalse(executionPlan.isComplete());
        
        executionPlan.moveToStep(3); // Beyond last step
        assertTrue(executionPlan.isComplete());
    }
    
    @Test
    public void testGetProgressPercentage() {
        assertEquals(0, executionPlan.getProgressPercentage());
        
        executionPlan.moveToNextStep();
        assertEquals(33, executionPlan.getProgressPercentage()); // 1/3 * 100 = 33
        
        executionPlan.moveToNextStep();
        assertEquals(66, executionPlan.getProgressPercentage()); // 2/3 * 100 = 66
        
        executionPlan.moveToNextStep();
        assertEquals(100, executionPlan.getProgressPercentage()); // 3/3 * 100 = 100
    }
    
    @Test
    public void testGetProgressPercentageEmptyPlan() {
        ExecutionPlan emptyPlan = new ExecutionPlan("Empty goal", new ArrayList<ExecutionStep>());
        assertEquals(100, emptyPlan.getProgressPercentage());
    }
    
    @Test
    public void testAddStep() {
        ExecutionStep newStep = new ExecutionStep(4, "General", "New operation", "Description", "Expected outcome");
        executionPlan.addStep(newStep);
        
        assertEquals(4, executionPlan.getTotalSteps());
        
        // Move to the new step
        executionPlan.moveToStep(3);
        ExecutionStep currentStep = executionPlan.getCurrentStep();
        assertEquals(4, currentStep.getStepNumber());
    }
    
    @Test
    public void testInsertStep() {
        ExecutionStep newStep = new ExecutionStep(4, "General", "Inserted operation", "Description", "Expected outcome");
        executionPlan.insertStep(1, newStep);
        
        assertEquals(4, executionPlan.getTotalSteps());
        assertEquals(1, executionPlan.getCurrentStepIndex()); // Should be adjusted
        
        // Check that the step was inserted correctly
        executionPlan.moveToStep(1);
        ExecutionStep insertedStep = executionPlan.getCurrentStep();
        assertEquals("Inserted operation", insertedStep.getOperation());
    }
    
    @Test
    public void testRemoveStep() {
        assertTrue(executionPlan.removeStep(1));
        assertEquals(2, executionPlan.getTotalSteps());
        
        // Current step index should remain 0
        assertEquals(0, executionPlan.getCurrentStepIndex());
        
        // Check that the correct step was removed
        executionPlan.moveToStep(1);
        ExecutionStep currentStep = executionPlan.getCurrentStep();
        assertEquals("Run tests", currentStep.getOperation()); // Should be the third original step
    }
    
    @Test
    public void testRemoveCurrentStep() {
        executionPlan.moveToStep(1);
        assertTrue(executionPlan.removeStep(1));
        
        assertEquals(2, executionPlan.getTotalSteps());
        assertEquals(1, executionPlan.getCurrentStepIndex());
        
        // Current step should now be the next step
        ExecutionStep currentStep = executionPlan.getCurrentStep();
        assertEquals("Run tests", currentStep.getOperation());
    }
    
    @Test
    public void testRemoveInvalidStep() {
        assertFalse(executionPlan.removeStep(-1));
        assertFalse(executionPlan.removeStep(10));
        assertEquals(3, executionPlan.getTotalSteps()); // Should remain unchanged
    }
    
    @Test
    public void testGetSummary() {
        String summary = executionPlan.getSummary();
        
        assertTrue(summary.contains("Create a new feature"));
        assertTrue(summary.contains("Total Steps: 3"));
        assertTrue(summary.contains("Current Step: 1/3"));
        assertTrue(summary.contains("Progress: 0%"));
        assertTrue(summary.contains("FileSystem: Read file [CURRENT]"));
        assertTrue(summary.contains("Editor: Open file [PENDING]"));
        assertTrue(summary.contains("Terminal: Run tests [PENDING]"));
    }
    
    @Test
    public void testGetSummaryWithProgress() {
        // Mark first step as completed and move to second
        steps.get(0).setStatus(ExecutionStatus.COMPLETED);
        executionPlan.moveToNextStep();
        
        String summary = executionPlan.getSummary();
        
        assertTrue(summary.contains("Current Step: 2/3"));
        assertTrue(summary.contains("Progress: 33%"));
        assertTrue(summary.contains("FileSystem: Read file [COMPLETED]"));
        assertTrue(summary.contains("Editor: Open file [CURRENT]"));
        assertTrue(summary.contains("Terminal: Run tests [PENDING]"));
    }
    
    @Test
    public void testToString() {
        String toString = executionPlan.toString();
        
        assertTrue(toString.contains("ExecutionPlan"));
        assertTrue(toString.contains("Create a new feature"));
        assertTrue(toString.contains("totalSteps=3"));
        assertTrue(toString.contains("currentStep=1"));
        assertTrue(toString.contains("progress=0%"));
    }
    
    @Test
    public void testCreatedTimestamp() {
        long before = System.currentTimeMillis();
        ExecutionPlan plan = new ExecutionPlan("Test", new ArrayList<ExecutionStep>());
        long after = System.currentTimeMillis();
        
        long timestamp = plan.getCreatedTimestamp();
        assertTrue(timestamp >= before && timestamp <= after);
    }
}
