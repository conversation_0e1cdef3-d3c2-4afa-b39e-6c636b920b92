package com.fartech.eclipse.aiagent.ui.actions;

import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IResource;
import org.eclipse.jface.action.IAction;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.ui.IObjectActionDelegate;
import org.eclipse.ui.IWorkbenchPart;
import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.PartInitException;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.ui.views.ChatView;

/**
 * Action for sending selected resources to the AI Agent.
 */
public class SendToAIAction implements IObjectActionDelegate {
    
    private AIAgentLogger logger;
    private IWorkbenchPart targetPart;
    private IResource selectedResource;
    
    public SendToAIAction() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void setActivePart(IAction action, IWorkbenchPart targetPart) {
        this.targetPart = targetPart;
    }
    
    @Override
    public void selectionChanged(IAction action, ISelection selection) {
        selectedResource = null;
        
        if (selection instanceof IStructuredSelection) {
            IStructuredSelection structuredSelection = (IStructuredSelection) selection;
            Object firstElement = structuredSelection.getFirstElement();
            
            if (firstElement instanceof IResource) {
                selectedResource = (IResource) firstElement;
                action.setEnabled(true);
            } else {
                action.setEnabled(false);
            }
        } else {
            action.setEnabled(false);
        }
    }
    
    @Override
    public void run(IAction action) {
        if (selectedResource == null) {
            logger.warn("No resource selected for AI analysis");
            return;
        }
        
        try {
            // Open the chat view
            IWorkbenchWindow window = targetPart.getSite().getWorkbenchWindow();
            ChatView chatView = (ChatView) window.getActivePage().showView(ChatView.ID);
            
            // Generate a context message about the selected resource
            String message = generateContextMessage(selectedResource);
            
            // TODO: Send the message to the chat view
            logger.info("Sent resource to AI: " + selectedResource.getFullPath());
            
        } catch (PartInitException e) {
            logger.error("Failed to open chat view", e);
        } catch (Exception e) {
            logger.error("Error sending resource to AI", e);
        }
    }
    
    private String generateContextMessage(IResource resource) {
        StringBuilder message = new StringBuilder();
        
        message.append("Please analyze this resource: ");
        message.append(resource.getFullPath().toString());
        
        if (resource instanceof IFile) {
            IFile file = (IFile) resource;
            String extension = file.getFileExtension();
            
            if (extension != null) {
                switch (extension.toLowerCase()) {
                    case "java":
                        message.append("\n\nThis is a Java source file. Please review the code structure, identify potential issues, and suggest improvements.");
                        break;
                    case "xml":
                        message.append("\n\nThis is an XML file. Please check the structure and validate the content.");
                        break;
                    case "properties":
                        message.append("\n\nThis is a properties file. Please review the configuration settings.");
                        break;
                    default:
                        message.append("\n\nPlease analyze this file and provide insights.");
                        break;
                }
            }
        } else {
            message.append("\n\nThis is a directory. Please analyze its structure and contents.");
        }
        
        return message.toString();
    }
}
