package com.fartech.eclipse.aiagent.providers;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;
import com.fartech.eclipse.aiagent.core.preferences.AIAgentPreferences;
import com.fartech.eclipse.aiagent.core.security.SecureStorage;
import com.fartech.eclipse.aiagent.providers.models.AIRequest;
import com.fartech.eclipse.aiagent.providers.models.AIResponse;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * Main client for communicating with AI providers.
 * Supports multiple providers through a pluggable architecture.
 */
public class AIProviderClient {
    
    private final AIAgentLogger logger;
    private final SecureStorage secureStorage;
    private final Gson gson;
    private CloseableHttpClient httpClient;
    
    private volatile boolean initialized = false;
    private volatile boolean shutdown = false;
    
    public AIProviderClient(SecureStorage secureStorage) {
        this.logger = Activator.getDefault().getLogger();
        this.secureStorage = secureStorage;
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();
    }
    
    /**
     * Initialize the AI Provider Client
     */
    public void initialize() throws Exception {
        if (initialized) {
            logger.warn("AI Provider Client already initialized");
            return;
        }
        
        try {
            logger.info("Initializing AI Provider Client...");
            
            // Create HTTP client with appropriate configuration
            int timeout = AIAgentPreferences.getPreferenceStore()
                .getInt(AIAgentPreferences.PREF_AI_PROVIDER_TIMEOUT);
            
            RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .build();
            
            httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .setMaxConnTotal(10)
                .setMaxConnPerRoute(5)
                .evictExpiredConnections()
                .evictIdleConnections(30, TimeUnit.SECONDS)
                .build();
            
            initialized = true;
            logger.info("AI Provider Client initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize AI Provider Client", e);
            throw e;
        }
    }
    
    /**
     * Send a request to the configured AI provider
     * @param request the AI request
     * @return the AI response
     * @throws AIProviderException if the request fails
     */
    public AIResponse sendRequest(AIRequest request) throws AIProviderException {
        if (!initialized) {
            throw new IllegalStateException("AI Provider Client not initialized");
        }
        
        if (shutdown) {
            throw new IllegalStateException("AI Provider Client is shut down");
        }
        
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
        
        try {
            logger.debug("Sending AI request: " + request.getPrompt().substring(0, Math.min(100, request.getPrompt().length())) + "...");
            
            // Get provider configuration
            String providerType = AIAgentPreferences.getPreferenceStore()
                .getString(AIAgentPreferences.PREF_AI_PROVIDER_TYPE);
            String endpoint = AIAgentPreferences.getPreferenceStore()
                .getString(AIAgentPreferences.PREF_AI_PROVIDER_ENDPOINT);
            String apiKey = secureStorage.getApiKey(providerType);
            
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new AIProviderException("No API key configured for provider: " + providerType);
            }
            
            // Create provider-specific handler
            AIProviderHandler handler = createProviderHandler(providerType);
            
            // Build HTTP request
            HttpPost httpPost = handler.buildHttpRequest(endpoint, apiKey, request);
            
            // Execute request
            HttpResponse response = httpClient.execute(httpPost);
            
            // Process response
            AIResponse aiResponse = handler.processResponse(response);
            
            logger.debug("AI request completed successfully");
            return aiResponse;
            
        } catch (AIProviderException e) {
            logger.error("AI Provider error: " + e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during AI request", e);
            throw new AIProviderException("Unexpected error during AI request", e);
        }
    }
    
    /**
     * Test connection to the configured AI provider
     * @return true if connection is successful, false otherwise
     */
    public boolean testConnection() {
        try {
            AIRequest testRequest = new AIRequest("Test connection", 10, 0.1);
            AIResponse response = sendRequest(testRequest);
            return response != null && response.isSuccess();
        } catch (Exception e) {
            logger.warn("Connection test failed", e);
            return false;
        }
    }
    
    /**
     * Create a provider-specific handler based on the provider type
     */
    private AIProviderHandler createProviderHandler(String providerType) throws AIProviderException {
        switch (providerType.toLowerCase()) {
            case "openai":
                return new OpenAIHandler(gson, logger);
            case "anthropic":
                return new AnthropicHandler(gson, logger);
            case "azure":
                return new AzureOpenAIHandler(gson, logger);
            case "local":
                return new LocalLLMHandler(gson, logger);
            case "custom":
                return new CustomProviderHandler(gson, logger);
            default:
                throw new AIProviderException("Unsupported provider type: " + providerType);
        }
    }
    
    /**
     * Shutdown the AI Provider Client and cleanup resources
     */
    public void shutdown() {
        if (shutdown) {
            return;
        }
        
        shutdown = true;
        logger.info("Shutting down AI Provider Client...");
        
        try {
            if (httpClient != null) {
                httpClient.close();
                httpClient = null;
            }
            
            logger.info("AI Provider Client shut down successfully");
            
        } catch (IOException e) {
            logger.error("Error during AI Provider Client shutdown", e);
        }
    }
    
    /**
     * Check if the client is initialized and ready to use
     */
    public boolean isReady() {
        return initialized && !shutdown;
    }
    
    /**
     * Get the current provider type
     */
    public String getCurrentProviderType() {
        return AIAgentPreferences.getPreferenceStore()
            .getString(AIAgentPreferences.PREF_AI_PROVIDER_TYPE);
    }
    
    /**
     * Check if API key is configured for the current provider
     */
    public boolean isApiKeyConfigured() {
        try {
            String providerType = getCurrentProviderType();
            String apiKey = secureStorage.getApiKey(providerType);
            return apiKey != null && !apiKey.trim().isEmpty();
        } catch (Exception e) {
            logger.warn("Error checking API key configuration", e);
            return false;
        }
    }
}
