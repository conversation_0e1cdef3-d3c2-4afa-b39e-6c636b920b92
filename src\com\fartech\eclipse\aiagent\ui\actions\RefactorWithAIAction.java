package com.fartech.eclipse.aiagent.ui.actions;

import org.eclipse.jface.action.IAction;
import org.eclipse.jface.text.ITextSelection;
import org.eclipse.jface.viewers.ISelection;
import org.eclipse.ui.IEditorActionDelegate;
import org.eclipse.ui.IEditorPart;

import com.fartech.eclipse.aiagent.Activator;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * Action for refactoring selected code with AI.
 */
public class RefactorWithAIAction implements IEditorActionDelegate {
    
    private AIAgentLogger logger;
    private IEditorPart targetEditor;
    private ITextSelection textSelection;
    
    public RefactorWithAIAction() {
        this.logger = Activator.getDefault().getLogger();
    }
    
    @Override
    public void setActiveEditor(IAction action, IEditorPart targetEditor) {
        this.targetEditor = targetEditor;
    }
    
    @Override
    public void selectionChanged(IAction action, ISelection selection) {
        if (selection instanceof ITextSelection) {
            textSelection = (ITextSelection) selection;
            action.setEnabled(textSelection.getLength() > 0);
        } else {
            textSelection = null;
            action.setEnabled(false);
        }
    }
    
    @Override
    public void run(IAction action) {
        if (textSelection == null || textSelection.getLength() == 0) {
            logger.warn("No code selected for refactoring");
            return;
        }
        
        try {
            String selectedText = textSelection.getText();
            logger.info("Refactoring selected code: " + selectedText.substring(0, Math.min(50, selectedText.length())) + "...");
            
            // TODO: Implement AI-powered refactoring
            logger.info("AI refactoring not yet implemented");
            
        } catch (Exception e) {
            logger.error("Error refactoring code", e);
        }
    }
}
