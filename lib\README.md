# Dependencies Directory

This directory contains the required JAR dependencies for the Eclipse AI Agent Plugin.

## Required JAR Files

The following JAR files need to be placed in this directory:

1. **httpclient-4.5.14.jar** - Apache HTTP Client
2. **httpcore-4.4.16.jar** - Apache HTTP Core
3. **gson-2.10.1.jar** - Google JSON library
4. **commons-logging-1.2.jar** - Apache Commons Logging

## Download Instructions

### Option 1: Maven Central (Recommended)

Download the JAR files from Maven Central:

1. **Apache HttpClient 4.5.14**
   - URL: https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar
   - Save as: `httpclient-4.5.14.jar`

2. **Apache HttpCore 4.4.16**
   - URL: https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar
   - Save as: `httpcore-4.4.16.jar`

3. **Gson 2.10.1**
   - URL: https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar
   - Save as: `gson-2.10.1.jar`

4. **Commons Logging 1.2**
   - URL: https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar
   - Save as: `commons-logging-1.2.jar`

### Option 2: Using Maven (Automated)

If you have Maven installed, run this command from the project root:

```bash
mvn dependency:copy-dependencies -DoutputDirectory=lib -DincludeScope=runtime
```

### Option 3: Using Gradle

If you prefer Gradle, create a simple build.gradle:

```gradle
configurations {
    deps
}

dependencies {
    deps 'org.apache.httpcomponents:httpclient:4.5.14'
    deps 'org.apache.httpcomponents:httpcore:4.4.16'
    deps 'com.google.code.gson:gson:2.10.1'
    deps 'commons-logging:commons-logging:1.2'
}

task copyDeps(type: Copy) {
    from configurations.deps
    into 'lib'
}
```

Then run: `gradle copyDeps`

## Verification

After downloading, verify you have these files in the `lib/` directory:

```
lib/
├── httpclient-4.5.14.jar
├── httpcore-4.4.16.jar
├── gson-2.10.1.jar
└── commons-logging-1.2.jar
```

## Java 8 Compatibility

All these dependencies are compatible with Java 8:
- Apache HttpClient 4.5.14 - Java 7+
- Apache HttpCore 4.4.16 - Java 7+
- Gson 2.10.1 - Java 7+
- Commons Logging 1.2 - Java 6+

## License Information

- **Apache HttpClient/HttpCore**: Apache License 2.0
- **Gson**: Apache License 2.0
- **Commons Logging**: Apache License 2.0

All dependencies use Apache License 2.0, which is compatible with most commercial and open-source projects.
