package com.fartech.eclipse.aiagent;

import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.ui.plugin.AbstractUIPlugin;
import org.osgi.framework.BundleContext;

import com.fartech.eclipse.aiagent.core.AIAgentCore;
import com.fartech.eclipse.aiagent.core.logging.AIAgentLogger;

/**
 * The activator class controls the plug-in life cycle
 */
public class Activator extends AbstractUIPlugin {

    // The plug-in ID
    public static final String PLUGIN_ID = "com.fartech.eclipse.aiagent"; //$NON-NLS-1$

    // The shared instance
    private static Activator plugin;
    
    // Core AI Agent instance
    private AIAgentCore aiAgentCore;
    
    // Logger instance
    private AIAgentLogger logger;

    /**
     * The constructor
     */
    public Activator() {
    }

    /*
     * (non-Javadoc)
     * @see org.eclipse.ui.plugin.AbstractUIPlugin#start(org.osgi.framework.BundleContext)
     */
    public void start(BundleContext context) throws Exception {
        super.start(context);
        plugin = this;
        
        // Initialize logger
        logger = new AIAgentLogger();
        logger.info("Starting AI Agent Plugin...");
        
        try {
            // Initialize AI Agent Core
            aiAgentCore = new AIAgentCore();
            aiAgentCore.initialize();
            
            logger.info("AI Agent Plugin started successfully");
        } catch (Exception e) {
            logger.error("Failed to start AI Agent Plugin", e);
            throw e;
        }
    }

    /*
     * (non-Javadoc)
     * @see org.eclipse.ui.plugin.AbstractUIPlugin#stop(org.osgi.framework.BundleContext)
     */
    public void stop(BundleContext context) throws Exception {
        try {
            if (aiAgentCore != null) {
                aiAgentCore.shutdown();
                aiAgentCore = null;
            }
            
            if (logger != null) {
                logger.info("AI Agent Plugin stopped");
                logger.shutdown();
                logger = null;
            }
        } catch (Exception e) {
            // Log error but don't prevent shutdown
            System.err.println("Error during AI Agent Plugin shutdown: " + e.getMessage());
        } finally {
            plugin = null;
            super.stop(context);
        }
    }

    /**
     * Returns the shared instance
     *
     * @return the shared instance
     */
    public static Activator getDefault() {
        return plugin;
    }

    /**
     * Returns an image descriptor for the image file at the given
     * plug-in relative path
     *
     * @param path the path
     * @return the image descriptor
     */
    public static ImageDescriptor getImageDescriptor(String path) {
        return imageDescriptorFromPlugin(PLUGIN_ID, path);
    }
    
    /**
     * Get the AI Agent Core instance
     * @return the AI Agent Core instance
     */
    public AIAgentCore getAIAgentCore() {
        return aiAgentCore;
    }
    
    /**
     * Get the logger instance
     * @return the logger instance
     */
    public AIAgentLogger getLogger() {
        return logger;
    }
}
