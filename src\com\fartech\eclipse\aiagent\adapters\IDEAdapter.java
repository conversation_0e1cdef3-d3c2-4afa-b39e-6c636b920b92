package com.fartech.eclipse.aiagent.adapters;

/**
 * Base interface for all IDE integration adapters.
 * Provides common lifecycle methods for adapter management.
 */
public interface IDEAdapter {
    
    /**
     * Initialize the adapter
     * @throws Exception if initialization fails
     */
    void initialize() throws Exception;
    
    /**
     * Check if the adapter is initialized and ready to use
     * @return true if ready, false otherwise
     */
    boolean isReady();
    
    /**
     * Shutdown the adapter and cleanup resources
     */
    void shutdown();
    
    /**
     * Get the adapter type identifier
     * @return the adapter type name
     */
    String getAdapterType();
    
    /**
     * Get a human-readable description of the adapter
     * @return the adapter description
     */
    String getDescription();
}
